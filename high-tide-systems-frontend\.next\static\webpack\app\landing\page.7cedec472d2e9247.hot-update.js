"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/landing/page",{

/***/ "(app-pages-browser)/./src/components/landing/Pricing.js":
/*!*******************************************!*\
  !*** ./src/components/landing/Pricing.js ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Info_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Info,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Info_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Info,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst Pricing = ()=>{\n    _s();\n    const [isAnnual, setIsAnnual] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userCount, setUserCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(5);\n    const basePrice = 19.90; // Preço base por usuário\n    // Função para calcular desconto baseado na quantidade de usuários\n    const getDiscountByUserCount = (users)=>{\n        if (users >= 200) return 40;\n        if (users >= 100) return 35;\n        if (users >= 50) return 25;\n        if (users >= 20) return 15;\n        if (users >= 5) return 10;\n        return 0;\n    };\n    // Função para calcular preços\n    const calculatePrice = (users)=>{\n        const discount = getDiscountByUserCount(users);\n        const totalWithoutDiscount = users * basePrice;\n        const discountAmount = totalWithoutDiscount * (discount / 100);\n        const finalPrice = totalWithoutDiscount - discountAmount;\n        return {\n            totalWithoutDiscount,\n            discountAmount,\n            finalPrice,\n            discount,\n            monthlyPrice: finalPrice,\n            yearlyPrice: finalPrice * 12\n        };\n    };\n    const pricing = calculatePrice(userCount);\n    // Opções de usuários predefinidas\n    const userOptions = [\n        1,\n        5,\n        10,\n        20,\n        50,\n        100,\n        200\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"pricing\",\n        className: \"py-16 bg-gray-50 dark:bg-gray-800\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 md:px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    className: \"text-center max-w-3xl mx-auto mb-10\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\",\n                            children: \"Pre\\xe7os flex\\xedveis baseados em usu\\xe1rios\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 dark:text-gray-300 mb-6\",\n                            children: \"Pague apenas pelos usu\\xe1rios que voc\\xea precisa. Escolha a quantidade ideal para o seu neg\\xf3cio.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"mr-3 \".concat(!isAnnual ? 'text-gray-900 dark:text-white font-medium' : 'text-gray-600 dark:text-gray-300'),\n                                    children: \"Mensal\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsAnnual(!isAnnual),\n                                    className: \"relative inline-flex h-6 w-14 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 \".concat(isAnnual ? 'bg-orange-600' : 'bg-gray-300 dark:bg-gray-600'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-block h-4 w-4 transform rounded-full bg-white transition \".concat(isAnnual ? 'translate-x-9' : 'translate-x-1')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                        lineNumber: 81,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-3 \".concat(isAnnual ? 'text-gray-900 dark:text-white font-medium' : 'text-gray-600 dark:text-gray-300'),\n                                    children: \"Anual\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, undefined),\n                activeTab === 'modules' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModulesView, {\n                    modules: modules,\n                    selectedModules: selectedModules,\n                    toggleModule: toggleModule,\n                    isAnnual: isAnnual,\n                    calculateMonthlyValue: calculateMonthlyValue,\n                    calculateTotal: calculateTotal\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                    lineNumber: 95,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ComparisonView, {\n                    modules: modules,\n                    isAnnual: isAnnual,\n                    toggleModule: toggleModule,\n                    setActiveTab: setActiveTab\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                    lineNumber: 104,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12 text-center max-w-5xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-300 mb-4\",\n                            children: \"Todos os planos incluem:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap justify-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white dark:bg-gray-900 p-4 rounded-lg shadow flex-1 min-w-[200px] max-w-[300px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-gray-900 dark:text-white mb-2\",\n                                            children: \"Suporte t\\xe9cnico\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 dark:text-gray-400 text-sm\",\n                                            children: \"Suporte por e-mail e chat em hor\\xe1rio comercial\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white dark:bg-gray-900 p-4 rounded-lg shadow flex-1 min-w-[200px] max-w-[300px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-gray-900 dark:text-white mb-2\",\n                                            children: \"Atualiza\\xe7\\xf5es\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 dark:text-gray-400 text-sm\",\n                                            children: \"Acesso a todas as atualiza\\xe7\\xf5es e novos recursos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white dark:bg-gray-900 p-4 rounded-lg shadow flex-1 min-w-[200px] max-w-[300px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-gray-900 dark:text-white mb-2\",\n                                            children: \"Seguran\\xe7a\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 dark:text-gray-400 text-sm\",\n                                            children: \"Backup di\\xe1rio e prote\\xe7\\xe3o de dados conforme LGPD\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-6 text-sm text-gray-500 dark:text-gray-400\",\n                            children: \"Pre\\xe7os para at\\xe9 5 usu\\xe1rios. Para equipes maiores ou necessidades espec\\xedficas, entre em contato para um or\\xe7amento personalizado.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Pricing, \"VTbvw8HivIaqBCZeV9Lfr85BgVo=\");\n_c = Pricing;\n// Componente para a visualização de módulos\nconst ModulesView = (param)=>{\n    let { modules: modules1, selectedModules: selectedModules1, toggleModule: toggleModule1, isAnnual, calculateMonthlyValue: calculateMonthlyValue1, calculateTotal: calculateTotal1 } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap justify-center gap-4 mb-8\",\n                children: modules1.map((module, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.5,\n                            delay: index * 0.1\n                        },\n                        className: \"bg-white dark:bg-gray-900 rounded-xl shadow-lg overflow-hidden border-2 transition-all flex-1 min-w-[250px] max-w-[350px] \".concat(selectedModules1.includes(module.id) ? (module.id === 'scheduler' ? 'border-purple-500 dark:border-purple-400' : module.id === 'people' ? 'border-amber-500 dark:border-amber-400' : module.id === 'financial' ? 'border-emerald-500 dark:border-emerald-400' : module.id === 'admin' ? 'border-red-500 dark:border-red-400' : 'border-blue-500 dark:border-blue-400') + ' transform scale-[1.02]' : 'border-transparent'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-5 flex flex-col h-full justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 rounded-lg flex items-center justify-center \".concat(module.colorClass),\n                                            children: module.icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-bold text-gray-900 dark:text-white\",\n                                                    children: module.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 dark:text-gray-400 text-sm\",\n                                                    children: [\n                                                        \"R$ \",\n                                                        isAnnual ? (module.annualPrice / 12).toFixed(0) : module.monthlyPrice,\n                                                        \"/m\\xeas\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 168,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300 mb-4 text-sm\",\n                                    children: module.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 178,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Info_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-500 mr-2 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600 dark:text-gray-300 text-sm\",\n                                                    children: module.features[0]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Info_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-500 mr-2 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600 dark:text-gray-300 text-sm\",\n                                                    children: module.features[1]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>toggleModule1(module.id),\n                                    className: \"w-full py-2 px-3 rounded-lg font-medium transition-colors text-sm \".concat(selectedModules1.includes(module.id) ? module.id === 'scheduler' ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 border border-purple-300 dark:border-purple-700' : module.id === 'people' ? 'bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-400 border border-amber-300 dark:border-amber-700' : module.id === 'financial' ? 'bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-400 border border-emerald-300 dark:border-emerald-700' : module.id === 'admin' ? 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 border border-red-300 dark:border-red-700' : 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 border border-blue-300 dark:border-blue-700' : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700 hover:bg-gray-200 dark:hover:bg-gray-700'),\n                                    children: selectedModules1.includes(module.id) ? 'Selecionado' : 'Selecionar'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, undefined)\n                    }, module.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                whileInView: {\n                    opacity: 1,\n                    y: 0\n                },\n                viewport: {\n                    once: true\n                },\n                transition: {\n                    duration: 0.5\n                },\n                className: \"max-w-5xl mx-auto bg-white dark:bg-gray-900 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full lg:w-1/2 mb-4 lg:mb-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-gray-900 dark:text-white mb-2\",\n                                    children: \"Resumo do seu plano\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, undefined),\n                                selectedModules1.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500 dark:text-gray-400 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Info_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 228,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Selecione pelo menos um m\\xf3dulo para ver o pre\\xe7o total\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 227,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: selectedModules1.map((moduleId)=>{\n                                        const module = modules1.find((m)=>m.id === moduleId);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center bg-gray-100 dark:bg-gray-800 rounded-full py-1 px-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-5 h-5 rounded-full flex items-center justify-center mr-1 \".concat(module.colorClass),\n                                                    children: module.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-700 dark:text-gray-300 text-sm\",\n                                                    children: module.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, moduleId, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 236,\n                                            columnNumber: 21\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 232,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full lg:w-1/2 text-center lg:text-right\",\n                            children: selectedModules1.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500 dark:text-gray-400 text-sm\",\n                                                children: \"Valor mensal:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                lineNumber: 252,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n                                                children: [\n                                                    \"R$ \",\n                                                    calculateMonthlyValue1(),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400 font-normal\",\n                                                        children: \"/m\\xeas\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                lineNumber: 253,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                        lineNumber: 251,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    isAnnual && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-500 dark:text-gray-400 text-sm mb-3\",\n                                        children: [\n                                            \"Valor total anual: R$ \",\n                                            calculateTotal1().toFixed(0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-500 ml-1\",\n                                                children: [\n                                                    \"(economia de R$ \",\n                                                    (calculateTotal1() * 0.2).toFixed(0),\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                lineNumber: 262,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                        lineNumber: 260,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    selectedModules1.length >= 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-green-500 text-sm mb-3\",\n                                        children: [\n                                            \"Desconto aplicado: \",\n                                            selectedModules1.length >= 3 ? '10%' : '5%'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                        lineNumber: 267,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-5 rounded-lg transition-colors\",\n                                        children: \"Come\\xe7ar agora\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                        lineNumber: 272,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                    lineNumber: 220,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_c1 = ModulesView;\n// Componente para a visualização de comparação\nconst ComparisonView = (param)=>{\n    let { modules: modules1, isAnnual, toggleModule: toggleModule1, setActiveTab: setActiveTab1 } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"overflow-x-auto mb-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n            className: \"w-full bg-white dark:bg-gray-900 rounded-xl shadow-lg overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                        className: \"bg-gray-100 dark:bg-purple-900/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"py-4 px-6 text-left text-gray-700 dark:text-gray-300 font-medium\",\n                                children: \"Recurso\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, undefined),\n                            modules1.map((module)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"py-4 px-6 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center justify-center w-8 h-8 rounded-lg mb-2 \".concat(module.colorClass),\n                                            children: module.icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 294,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-900 dark:text-white font-bold\",\n                                            children: module.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 297,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-500 dark:text-gray-400 text-sm\",\n                                            children: [\n                                                \"R$ \",\n                                                isAnnual ? (module.annualPrice / 12).toFixed(0) : module.monthlyPrice,\n                                                \"/m\\xeas\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, module.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 293,\n                                    columnNumber: 15\n                                }, undefined))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                        lineNumber: 290,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                    lineNumber: 289,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                    children: [\n                        [\n                            'Usuários incluídos',\n                            'Armazenamento',\n                            'Suporte',\n                            'API Access',\n                            'Integrações'\n                        ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"border-t border-gray-200 dark:border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"py-3 px-6 text-left text-gray-700 dark:text-gray-300\",\n                                        children: feature\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    modules1.map((module)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-3 px-6 text-center text-gray-600 dark:text-gray-400\",\n                                            children: [\n                                                feature === 'Usuários incluídos' && '5 usuários',\n                                                feature === 'Armazenamento' && (module.id === 'admin' ? '10GB' : '5GB'),\n                                                feature === 'Suporte' && (module.id === 'financial' || module.id === 'admin' ? 'Prioritário' : 'Padrão'),\n                                                feature === 'API Access' && (module.id === 'admin' || module.id === 'reports' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Info_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-5 w-5 text-green-500 mx-auto\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 25\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(X, {\n                                                    className: \"h-5 w-5 text-red-500 mx-auto\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 25\n                                                }, undefined)),\n                                                feature === 'Integrações' && (module.id === 'financial' || module.id === 'scheduler' || module.id === 'admin' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Info_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-5 w-5 text-green-500 mx-auto\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 25\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(X, {\n                                                    className: \"h-5 w-5 text-red-500 mx-auto\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 25\n                                                }, undefined))\n                                            ]\n                                        }, module.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 311,\n                                            columnNumber: 17\n                                        }, undefined))\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, undefined)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            className: \"border-t border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"py-4 px-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, undefined),\n                                modules1.map((module)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"py-4 px-6 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setActiveTab1('modules');\n                                                setTimeout(()=>{\n                                                    scrollToElement('pricing', 80);\n                                                    toggleModule1(module.id);\n                                                }, 100);\n                                            },\n                                            className: \"py-2 px-4 rounded-lg font-medium text-sm transition-colors \".concat(module.id === 'scheduler' ? 'bg-purple-600 hover:bg-purple-700 text-white' : module.id === 'people' ? 'bg-amber-600 hover:bg-amber-700 text-white' : module.id === 'financial' ? 'bg-emerald-600 hover:bg-emerald-700 text-white' : module.id === 'admin' ? 'bg-red-600 hover:bg-red-700 text-white' : 'bg-blue-600 hover:bg-blue-700 text-white'),\n                                            children: \"Selecionar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 339,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, module.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                        lineNumber: 338,\n                                        columnNumber: 15\n                                    }, undefined))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                    lineNumber: 305,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n            lineNumber: 288,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n        lineNumber: 287,\n        columnNumber: 5\n    }, undefined);\n};\n_c2 = ComparisonView;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Pricing);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Pricing\");\n$RefreshReg$(_c1, \"ModulesView\");\n$RefreshReg$(_c2, \"ComparisonView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/Pricing.js\n"));

/***/ })

});