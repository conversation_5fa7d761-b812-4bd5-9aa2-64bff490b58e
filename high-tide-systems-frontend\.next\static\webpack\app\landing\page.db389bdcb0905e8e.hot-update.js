"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/landing/page",{

/***/ "(app-pages-browser)/./src/components/landing/Features.js":
/*!********************************************!*\
  !*** ./src/components/landing/Features.js ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart4,Bell,Calendar,CheckCircle,Clock,CreditCard,MessageSquare,Shield,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart4,Bell,Calendar,CheckCircle,Clock,CreditCard,MessageSquare,Shield,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart4,Bell,Calendar,CheckCircle,Clock,CreditCard,MessageSquare,Shield,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart4,Bell,Calendar,CheckCircle,Clock,CreditCard,MessageSquare,Shield,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart4,Bell,Calendar,CheckCircle,Clock,CreditCard,MessageSquare,Shield,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column-increasing.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart4,Bell,Calendar,CheckCircle,Clock,CreditCard,MessageSquare,Shield,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart4,Bell,Calendar,CheckCircle,Clock,CreditCard,MessageSquare,Shield,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart4,Bell,Calendar,CheckCircle,Clock,CreditCard,MessageSquare,Shield,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart4,Bell,Calendar,CheckCircle,Clock,CreditCard,MessageSquare,Shield,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst features = [\n    {\n        id: 'scheduling',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n            lineNumber: 22,\n            columnNumber: 11\n        }, undefined),\n        title: 'Agendamento Inteligente',\n        description: 'Gerencie consultas e compromissos com facilidade. Visualização em calendário diário, semanal e mensal com detecção automática de conflitos.',\n        color: 'purple',\n        benefits: [\n            'Redução de 70% no tempo gasto com agendamentos',\n            'Eliminação de conflitos de horários',\n            'Visualização clara da agenda de cada profissional',\n            'Agendamentos recorrentes com um clique',\n            'Confirmação automática por e-mail e SMS'\n        ],\n        image: 'landing/features/scheduling.png'\n    },\n    {\n        id: 'patients',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n            lineNumber: 37,\n            columnNumber: 11\n        }, undefined),\n        title: 'Gestão de Pacientes',\n        description: 'Cadastro completo de pacientes com histórico médico, documentos, convênios e relacionamentos familiares.',\n        color: 'amber',\n        benefits: [\n            'Prontuário eletrônico completo e seguro',\n            'Histórico de atendimentos e evolução',\n            'Gestão de documentos digitalizados',\n            'Controle de convênios e planos de saúde',\n            'Relacionamentos familiares e contatos de emergência'\n        ],\n        image: '/features/patients.png'\n    },\n    {\n        id: 'working-hours',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n            lineNumber: 52,\n            columnNumber: 11\n        }, undefined),\n        title: 'Horários de Trabalho',\n        description: 'Configure a disponibilidade de cada profissional, incluindo intervalos, folgas e feriados para otimizar o agendamento.',\n        color: 'blue',\n        benefits: [\n            'Configuração flexível de horários por profissional',\n            'Definição de intervalos e pausas',\n            'Calendário de folgas e feriados',\n            'Bloqueio de horários para reuniões e eventos',\n            'Visualização da ocupação em tempo real'\n        ],\n        image: '/features/working-hours.png'\n    },\n    {\n        id: 'notifications',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n            lineNumber: 67,\n            columnNumber: 11\n        }, undefined),\n        title: 'Notificações Automáticas',\n        description: 'Lembretes de consultas por e-mail e sistema para reduzir faltas e aumentar a satisfação dos pacientes.',\n        color: 'red',\n        benefits: [\n            'Redução de até 40% nas faltas de pacientes',\n            'Lembretes personalizáveis por tipo de atendimento',\n            'Confirmação de presença com um clique',\n            'Notificações para a equipe sobre alterações',\n            'Alertas de aniversários e datas importantes'\n        ],\n        image: '/features/notifications.png'\n    },\n    {\n        id: 'reports',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n            lineNumber: 83,\n            columnNumber: 11\n        }, undefined),\n        title: 'Relatórios e Dashboards',\n        description: 'Análises detalhadas de desempenho, ocupação, faturamento e outros indicadores importantes para sua clínica.',\n        color: 'indigo',\n        benefits: [\n            'Dashboard interativo com KPIs em tempo real',\n            'Relatórios personalizáveis por período',\n            'Análise de ocupação e produtividade',\n            'Indicadores de desempenho e produtividade',\n            'Exportação em diversos formatos (PDF, Excel, CSV)'\n        ],\n        image: '/features/reports.png'\n    },\n    {\n        id: 'chat',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n            lineNumber: 98,\n            columnNumber: 11\n        }, undefined),\n        title: 'Chat Integrado',\n        description: 'Comunicação interna entre profissionais e equipe administrativa para agilizar processos e melhorar a colaboração.',\n        color: 'orange',\n        benefits: [\n            'Comunicação em tempo real entre a equipe',\n            'Grupos por departamento ou função',\n            'Compartilhamento de arquivos e imagens',\n            'Histórico completo de conversas',\n            'Notificações de mensagens importantes'\n        ],\n        image: '/features/chat.png'\n    },\n    {\n        id: 'security',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n            lineNumber: 113,\n            columnNumber: 11\n        }, undefined),\n        title: 'Segurança e Privacidade',\n        description: 'Controle de acesso por perfil, registro de atividades e conformidade com LGPD para proteger dados sensíveis.',\n        color: 'rose',\n        benefits: [\n            'Conformidade total com a LGPD',\n            'Controle de acesso granular por perfil',\n            'Registro detalhado de todas as atividades',\n            'Criptografia de dados sensíveis',\n            'Backups automáticos e redundantes'\n        ],\n        image: '/features/security.png'\n    },\n    {\n        id: 'patient-portal',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Smartphone, {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n            lineNumber: 128,\n            columnNumber: 11\n        }, undefined),\n        title: 'Acesso para Pacientes',\n        description: 'Portal do paciente para visualização de agendamentos, histórico e atualização de dados pessoais.',\n        color: 'cyan',\n        benefits: [\n            'Agendamento online de consultas',\n            'Visualização de histórico de atendimentos',\n            'Acesso a resultados de exames',\n            'Atualização de dados cadastrais',\n            'Comunicação direta com a clínica'\n        ],\n        image: '/features/patient-portal.png'\n    }\n];\nconst Features = ()=>{\n    _s();\n    const [activeFeature, setActiveFeature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const getColorClasses = (color, isActive)=>{\n        const colorMap = {\n            purple: {\n                bg: isActive ? 'bg-purple-500' : 'bg-purple-100 dark:bg-purple-900/30',\n                text: isActive ? 'text-white' : 'text-purple-500 dark:text-purple-400',\n                border: 'border-purple-200 dark:border-purple-800',\n                hover: 'hover:bg-purple-50 dark:hover:bg-purple-900/40',\n                shadow: 'shadow-purple-100 dark:shadow-purple-900/20'\n            },\n            amber: {\n                bg: isActive ? 'bg-amber-500' : 'bg-amber-100 dark:bg-amber-900/30',\n                text: isActive ? 'text-white' : 'text-amber-500 dark:text-amber-400',\n                border: 'border-amber-200 dark:border-amber-800',\n                hover: 'hover:bg-amber-50 dark:hover:bg-amber-900/40',\n                shadow: 'shadow-amber-100 dark:shadow-amber-900/20'\n            },\n            blue: {\n                bg: isActive ? 'bg-blue-500' : 'bg-blue-100 dark:bg-blue-900/30',\n                text: isActive ? 'text-white' : 'text-blue-500 dark:text-blue-400',\n                border: 'border-blue-200 dark:border-blue-800',\n                hover: 'hover:bg-blue-50 dark:hover:bg-blue-900/40',\n                shadow: 'shadow-blue-100 dark:shadow-blue-900/20'\n            },\n            red: {\n                bg: isActive ? 'bg-red-500' : 'bg-red-100 dark:bg-red-900/30',\n                text: isActive ? 'text-white' : 'text-red-500 dark:text-red-400',\n                border: 'border-red-200 dark:border-red-800',\n                hover: 'hover:bg-red-50 dark:hover:bg-red-900/40',\n                shadow: 'shadow-red-100 dark:shadow-red-900/20'\n            },\n            emerald: {\n                bg: isActive ? 'bg-emerald-500' : 'bg-emerald-100 dark:bg-emerald-900/30',\n                text: isActive ? 'text-white' : 'text-emerald-500 dark:text-emerald-400',\n                border: 'border-emerald-200 dark:border-emerald-800',\n                hover: 'hover:bg-emerald-50 dark:hover:bg-emerald-900/40',\n                shadow: 'shadow-emerald-100 dark:shadow-emerald-900/20'\n            },\n            indigo: {\n                bg: isActive ? 'bg-indigo-500' : 'bg-indigo-100 dark:bg-indigo-900/30',\n                text: isActive ? 'text-white' : 'text-indigo-500 dark:text-indigo-400',\n                border: 'border-indigo-200 dark:border-indigo-800',\n                hover: 'hover:bg-indigo-50 dark:hover:bg-indigo-900/40',\n                shadow: 'shadow-indigo-100 dark:shadow-indigo-900/20'\n            },\n            orange: {\n                bg: isActive ? 'bg-orange-500' : 'bg-orange-100 dark:bg-orange-900/30',\n                text: isActive ? 'text-white' : 'text-orange-500 dark:text-orange-400',\n                border: 'border-orange-200 dark:border-orange-800',\n                hover: 'hover:bg-orange-50 dark:hover:bg-orange-900/40',\n                shadow: 'shadow-orange-100 dark:shadow-orange-900/20'\n            },\n            rose: {\n                bg: isActive ? 'bg-rose-500' : 'bg-rose-100 dark:bg-rose-900/30',\n                text: isActive ? 'text-white' : 'text-rose-500 dark:text-rose-400',\n                border: 'border-rose-200 dark:border-rose-800',\n                hover: 'hover:bg-rose-50 dark:hover:bg-rose-900/40',\n                shadow: 'shadow-rose-100 dark:shadow-rose-900/20'\n            },\n            cyan: {\n                bg: isActive ? 'bg-cyan-500' : 'bg-cyan-100 dark:bg-cyan-900/30',\n                text: isActive ? 'text-white' : 'text-cyan-500 dark:text-cyan-400',\n                border: 'border-cyan-200 dark:border-cyan-800',\n                hover: 'hover:bg-cyan-50 dark:hover:bg-cyan-900/40',\n                shadow: 'shadow-cyan-100 dark:shadow-cyan-900/20'\n            }\n        };\n        return colorMap[color] || colorMap.purple;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"features\",\n        className: \"py-20 bg-white dark:bg-gray-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 md:px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    className: \"text-center max-w-3xl mx-auto mb-16\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\",\n                            children: \"Recursos completos para sua cl\\xednica\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 dark:text-gray-300\",\n                            children: \"Tudo o que voc\\xea precisa para gerenciar sua cl\\xednica ou consult\\xf3rio em uma \\xfanica plataforma intuitiva e poderosa.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                    lineNumber: 219,\n                    columnNumber: 9\n                }, undefined),\n                activeFeature && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    className: \"mb-16 bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden border border-gray-100 dark:border-gray-700\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 dark:bg-gray-900 p-8 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative w-full h-64 md:h-80 lg:h-full rounded-xl overflow-hidden bg-gray-200 dark:bg-gray-700 flex items-center justify-center\",\n                                    children: activeFeature.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: activeFeature.image,\n                                        alt: activeFeature.title,\n                                        className: \"w-full h-full object-cover\",\n                                        onError: (e)=>{\n                                            e.target.onerror = null;\n                                            e.target.style.display = 'none';\n                                            e.target.parentElement.innerHTML = '\\n                          <div class=\"text-center p-8\">\\n                            <div class=\"'.concat(getColorClasses(activeFeature.color, false).text, ' mx-auto mb-4\">\\n                              ').concat(activeFeature.icon.props.children, '\\n                            </div>\\n                            <p class=\"text-gray-500 dark:text-gray-400\">Visualiza\\xe7\\xe3o do recurso</p>\\n                          </div>\\n                        ');\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                        lineNumber: 247,\n                                        columnNumber: 21\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center p-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\".concat(getColorClasses(activeFeature.color, false).text, \" mx-auto mb-4\"),\n                                                children: activeFeature.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                                lineNumber: 266,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 dark:text-gray-400\",\n                                                children: \"Visualiza\\xe7\\xe3o do recurso\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                                lineNumber: 269,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                        lineNumber: 265,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                    lineNumber: 245,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                lineNumber: 244,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-lg \".concat(getColorClasses(activeFeature.color, true).bg, \" flex items-center justify-center mr-4\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white\",\n                                                    children: activeFeature.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                                lineNumber: 278,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                children: activeFeature.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                                lineNumber: 283,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                        lineNumber: 277,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-300 mb-6\",\n                                        children: activeFeature.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white mb-4\",\n                                        children: \"Principais benef\\xedcios:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                        lineNumber: 292,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3 mb-8\",\n                                        children: activeFeature.benefits.map((benefit, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5 mt-0.5 mr-3 \".concat(getColorClasses(activeFeature.color, false).text)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 dark:text-gray-300\",\n                                                        children: benefit\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, i, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                                lineNumber: 298,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                        lineNumber: 296,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveFeature(null),\n                                        className: \"text-primary-500 dark:text-primary-400 hover:underline flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1 rotate-180\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                                lineNumber: 309,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Voltar para todos os recursos\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                        lineNumber: 305,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                lineNumber: 276,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                        lineNumber: 242,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                    lineNumber: 236,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 \".concat(activeFeature ? 'hidden lg:grid' : ''),\n                    children: features.map((feature, index)=>{\n                        const colorClasses = getColorClasses(feature.color, false);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            className: \"bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm hover:shadow-lg transition-all duration-300 border \".concat(colorClasses.border, \" \").concat(colorClasses.hover, \" cursor-pointer\"),\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            transition: {\n                                duration: 0.5,\n                                delay: index * 0.1\n                            },\n                            onClick: ()=>setActiveFeature(feature),\n                            whileHover: {\n                                y: -5\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 \".concat(colorClasses.bg, \" rounded-lg flex items-center justify-center mb-4\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: colorClasses.text,\n                                        children: feature.icon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                        lineNumber: 334,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                    lineNumber: 333,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white mb-2\",\n                                    children: feature.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                    lineNumber: 338,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300 mb-4\",\n                                    children: feature.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                    lineNumber: 341,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center text-sm font-medium text-primary-500 dark:text-primary-400\",\n                                    children: [\n                                        \"Ver detalhes\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"ml-1 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                            lineNumber: 346,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                    lineNumber: 344,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, feature.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                            lineNumber: 323,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                    lineNumber: 318,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n            lineNumber: 218,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Features, \"nAw/47mC1MxrYcjbU0YYaGlrYX0=\");\n_c = Features;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Features);\nvar _c;\n$RefreshReg$(_c, \"Features\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/Features.js\n"));

/***/ })

});