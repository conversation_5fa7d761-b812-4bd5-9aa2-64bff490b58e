"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/landing/page",{

/***/ "(app-pages-browser)/./src/components/landing/Features.js":
/*!********************************************!*\
  !*** ./src/components/landing/Features.js ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart4,Bell,Calendar,CheckCircle,Clock,CreditCard,MessageSquare,Shield,Smartphone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart4,Bell,Calendar,CheckCircle,Clock,CreditCard,MessageSquare,Shield,Smartphone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart4,Bell,Calendar,CheckCircle,Clock,CreditCard,MessageSquare,Shield,Smartphone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart4,Bell,Calendar,CheckCircle,Clock,CreditCard,MessageSquare,Shield,Smartphone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart4,Bell,Calendar,CheckCircle,Clock,CreditCard,MessageSquare,Shield,Smartphone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column-increasing.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart4,Bell,Calendar,CheckCircle,Clock,CreditCard,MessageSquare,Shield,Smartphone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart4,Bell,Calendar,CheckCircle,Clock,CreditCard,MessageSquare,Shield,Smartphone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart4,Bell,Calendar,CheckCircle,Clock,CreditCard,MessageSquare,Shield,Smartphone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart4,Bell,Calendar,CheckCircle,Clock,CreditCard,MessageSquare,Shield,Smartphone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart4,Bell,Calendar,CheckCircle,Clock,CreditCard,MessageSquare,Shield,Smartphone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst features = [\n    {\n        id: 'scheduling',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n            lineNumber: 22,\n            columnNumber: 11\n        }, undefined),\n        title: 'Agendamento Inteligente',\n        description: 'Gerencie consultas e compromissos com facilidade. Visualização em calendário diário, semanal e mensal com detecção automática de conflitos.',\n        color: 'purple',\n        benefits: [\n            'Redução de 70% no tempo gasto com agendamentos',\n            'Eliminação de conflitos de horários',\n            'Visualização clara da agenda de cada profissional',\n            'Agendamentos recorrentes com um clique',\n            'Confirmação automática por e-mail e SMS'\n        ],\n        image: 'landing/features/scheduling.png'\n    },\n    {\n        id: 'patients',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n            lineNumber: 37,\n            columnNumber: 11\n        }, undefined),\n        title: 'Gestão de Pacientes',\n        description: 'Cadastro completo de pacientes com histórico médico, documentos, convênios e relacionamentos familiares.',\n        color: 'amber',\n        benefits: [\n            'Prontuário eletrônico completo e seguro',\n            'Histórico de atendimentos e evolução',\n            'Gestão de documentos digitalizados',\n            'Controle de convênios e planos de saúde',\n            'Relacionamentos familiares e contatos de emergência'\n        ],\n        image: '/features/patients.png'\n    },\n    {\n        id: 'working-hours',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n            lineNumber: 52,\n            columnNumber: 11\n        }, undefined),\n        title: 'Horários de Trabalho',\n        description: 'Configure a disponibilidade de cada profissional, incluindo intervalos, folgas e feriados para otimizar o agendamento.',\n        color: 'blue',\n        benefits: [\n            'Configuração flexível de horários por profissional',\n            'Definição de intervalos e pausas',\n            'Calendário de folgas e feriados',\n            'Bloqueio de horários para reuniões e eventos',\n            'Visualização da ocupação em tempo real'\n        ],\n        image: '/features/working-hours.png'\n    },\n    {\n        id: 'notifications',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n            lineNumber: 67,\n            columnNumber: 11\n        }, undefined),\n        title: 'Notificações Automáticas',\n        description: 'Lembretes de consultas por e-mail e sistema para reduzir faltas e aumentar a satisfação dos pacientes.',\n        color: 'red',\n        benefits: [\n            'Redução de até 40% nas faltas de pacientes',\n            'Lembretes personalizáveis por tipo de atendimento',\n            'Confirmação de presença com um clique',\n            'Notificações para a equipe sobre alterações',\n            'Alertas de aniversários e datas importantes'\n        ],\n        image: '/features/notifications.png'\n    },\n    {\n        id: 'reports',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n            lineNumber: 83,\n            columnNumber: 11\n        }, undefined),\n        title: 'Relatórios e Dashboards',\n        description: 'Análises detalhadas de desempenho, ocupação, faturamento e outros indicadores importantes para sua clínica.',\n        color: 'indigo',\n        benefits: [\n            'Dashboard interativo com KPIs em tempo real',\n            'Relatórios personalizáveis por período',\n            'Análise de ocupação e produtividade',\n            'Indicadores financeiros e de desempenho',\n            'Exportação em diversos formatos (PDF, Excel, CSV)'\n        ],\n        image: '/features/reports.png'\n    },\n    {\n        id: 'chat',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n            lineNumber: 98,\n            columnNumber: 11\n        }, undefined),\n        title: 'Chat Integrado',\n        description: 'Comunicação interna entre profissionais e equipe administrativa para agilizar processos e melhorar a colaboração.',\n        color: 'orange',\n        benefits: [\n            'Comunicação em tempo real entre a equipe',\n            'Grupos por departamento ou função',\n            'Compartilhamento de arquivos e imagens',\n            'Histórico completo de conversas',\n            'Notificações de mensagens importantes'\n        ],\n        image: '/features/chat.png'\n    },\n    {\n        id: 'security',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n            lineNumber: 113,\n            columnNumber: 11\n        }, undefined),\n        title: 'Segurança e Privacidade',\n        description: 'Controle de acesso por perfil, registro de atividades e conformidade com LGPD para proteger dados sensíveis.',\n        color: 'rose',\n        benefits: [\n            'Conformidade total com a LGPD',\n            'Controle de acesso granular por perfil',\n            'Registro detalhado de todas as atividades',\n            'Criptografia de dados sensíveis',\n            'Backups automáticos e redundantes'\n        ],\n        image: '/features/security.png'\n    },\n    {\n        id: 'patient-portal',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n            lineNumber: 128,\n            columnNumber: 11\n        }, undefined),\n        title: 'Acesso para Pacientes',\n        description: 'Portal do paciente para visualização de agendamentos, histórico e atualização de dados pessoais.',\n        color: 'cyan',\n        benefits: [\n            'Agendamento online de consultas',\n            'Visualização de histórico de atendimentos',\n            'Acesso a resultados de exames',\n            'Atualização de dados cadastrais',\n            'Comunicação direta com a clínica'\n        ],\n        image: '/features/patient-portal.png'\n    }\n];\nconst Features = ()=>{\n    _s();\n    const [activeFeature, setActiveFeature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const getColorClasses = (color, isActive)=>{\n        const colorMap = {\n            purple: {\n                bg: isActive ? 'bg-purple-500' : 'bg-purple-100 dark:bg-purple-900/30',\n                text: isActive ? 'text-white' : 'text-purple-500 dark:text-purple-400',\n                border: 'border-purple-200 dark:border-purple-800',\n                hover: 'hover:bg-purple-50 dark:hover:bg-purple-900/40',\n                shadow: 'shadow-purple-100 dark:shadow-purple-900/20'\n            },\n            amber: {\n                bg: isActive ? 'bg-amber-500' : 'bg-amber-100 dark:bg-amber-900/30',\n                text: isActive ? 'text-white' : 'text-amber-500 dark:text-amber-400',\n                border: 'border-amber-200 dark:border-amber-800',\n                hover: 'hover:bg-amber-50 dark:hover:bg-amber-900/40',\n                shadow: 'shadow-amber-100 dark:shadow-amber-900/20'\n            },\n            blue: {\n                bg: isActive ? 'bg-blue-500' : 'bg-blue-100 dark:bg-blue-900/30',\n                text: isActive ? 'text-white' : 'text-blue-500 dark:text-blue-400',\n                border: 'border-blue-200 dark:border-blue-800',\n                hover: 'hover:bg-blue-50 dark:hover:bg-blue-900/40',\n                shadow: 'shadow-blue-100 dark:shadow-blue-900/20'\n            },\n            red: {\n                bg: isActive ? 'bg-red-500' : 'bg-red-100 dark:bg-red-900/30',\n                text: isActive ? 'text-white' : 'text-red-500 dark:text-red-400',\n                border: 'border-red-200 dark:border-red-800',\n                hover: 'hover:bg-red-50 dark:hover:bg-red-900/40',\n                shadow: 'shadow-red-100 dark:shadow-red-900/20'\n            },\n            emerald: {\n                bg: isActive ? 'bg-emerald-500' : 'bg-emerald-100 dark:bg-emerald-900/30',\n                text: isActive ? 'text-white' : 'text-emerald-500 dark:text-emerald-400',\n                border: 'border-emerald-200 dark:border-emerald-800',\n                hover: 'hover:bg-emerald-50 dark:hover:bg-emerald-900/40',\n                shadow: 'shadow-emerald-100 dark:shadow-emerald-900/20'\n            },\n            indigo: {\n                bg: isActive ? 'bg-indigo-500' : 'bg-indigo-100 dark:bg-indigo-900/30',\n                text: isActive ? 'text-white' : 'text-indigo-500 dark:text-indigo-400',\n                border: 'border-indigo-200 dark:border-indigo-800',\n                hover: 'hover:bg-indigo-50 dark:hover:bg-indigo-900/40',\n                shadow: 'shadow-indigo-100 dark:shadow-indigo-900/20'\n            },\n            orange: {\n                bg: isActive ? 'bg-orange-500' : 'bg-orange-100 dark:bg-orange-900/30',\n                text: isActive ? 'text-white' : 'text-orange-500 dark:text-orange-400',\n                border: 'border-orange-200 dark:border-orange-800',\n                hover: 'hover:bg-orange-50 dark:hover:bg-orange-900/40',\n                shadow: 'shadow-orange-100 dark:shadow-orange-900/20'\n            },\n            rose: {\n                bg: isActive ? 'bg-rose-500' : 'bg-rose-100 dark:bg-rose-900/30',\n                text: isActive ? 'text-white' : 'text-rose-500 dark:text-rose-400',\n                border: 'border-rose-200 dark:border-rose-800',\n                hover: 'hover:bg-rose-50 dark:hover:bg-rose-900/40',\n                shadow: 'shadow-rose-100 dark:shadow-rose-900/20'\n            },\n            cyan: {\n                bg: isActive ? 'bg-cyan-500' : 'bg-cyan-100 dark:bg-cyan-900/30',\n                text: isActive ? 'text-white' : 'text-cyan-500 dark:text-cyan-400',\n                border: 'border-cyan-200 dark:border-cyan-800',\n                hover: 'hover:bg-cyan-50 dark:hover:bg-cyan-900/40',\n                shadow: 'shadow-cyan-100 dark:shadow-cyan-900/20'\n            }\n        };\n        return colorMap[color] || colorMap.purple;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"features\",\n        className: \"py-20 bg-white dark:bg-gray-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 md:px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                    className: \"text-center max-w-3xl mx-auto mb-16\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\",\n                            children: \"Recursos completos para sua cl\\xednica\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 dark:text-gray-300\",\n                            children: \"Tudo o que voc\\xea precisa para gerenciar sua cl\\xednica ou consult\\xf3rio em uma \\xfanica plataforma intuitiva e poderosa.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                    lineNumber: 219,\n                    columnNumber: 9\n                }, undefined),\n                activeFeature && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                    className: \"mb-16 bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden border border-gray-100 dark:border-gray-700\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 dark:bg-gray-900 p-8 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative w-full h-64 md:h-80 lg:h-full rounded-xl overflow-hidden bg-gray-200 dark:bg-gray-700 flex items-center justify-center\",\n                                    children: activeFeature.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: activeFeature.image,\n                                        alt: activeFeature.title,\n                                        className: \"w-full h-full object-cover\",\n                                        onError: (e)=>{\n                                            e.target.onerror = null;\n                                            e.target.style.display = 'none';\n                                            e.target.parentElement.innerHTML = '\\n                          <div class=\"text-center p-8\">\\n                            <div class=\"'.concat(getColorClasses(activeFeature.color, false).text, ' mx-auto mb-4\">\\n                              ').concat(activeFeature.icon.props.children, '\\n                            </div>\\n                            <p class=\"text-gray-500 dark:text-gray-400\">Visualiza\\xe7\\xe3o do recurso</p>\\n                          </div>\\n                        ');\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                        lineNumber: 247,\n                                        columnNumber: 21\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center p-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\".concat(getColorClasses(activeFeature.color, false).text, \" mx-auto mb-4\"),\n                                                children: activeFeature.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                                lineNumber: 266,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 dark:text-gray-400\",\n                                                children: \"Visualiza\\xe7\\xe3o do recurso\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                                lineNumber: 269,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                        lineNumber: 265,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                    lineNumber: 245,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                lineNumber: 244,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-lg \".concat(getColorClasses(activeFeature.color, true).bg, \" flex items-center justify-center mr-4\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white\",\n                                                    children: activeFeature.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                                lineNumber: 278,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                children: activeFeature.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                                lineNumber: 283,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                        lineNumber: 277,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-300 mb-6\",\n                                        children: activeFeature.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white mb-4\",\n                                        children: \"Principais benef\\xedcios:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                        lineNumber: 292,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3 mb-8\",\n                                        children: activeFeature.benefits.map((benefit, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5 mt-0.5 mr-3 \".concat(getColorClasses(activeFeature.color, false).text)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 dark:text-gray-300\",\n                                                        children: benefit\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, i, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                                lineNumber: 298,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                        lineNumber: 296,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveFeature(null),\n                                        className: \"text-primary-500 dark:text-primary-400 hover:underline flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1 rotate-180\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                                lineNumber: 309,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Voltar para todos os recursos\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                        lineNumber: 305,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                lineNumber: 276,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                        lineNumber: 242,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                    lineNumber: 236,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 \".concat(activeFeature ? 'hidden lg:grid' : ''),\n                    children: features.map((feature, index)=>{\n                        const colorClasses = getColorClasses(feature.color, false);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                            className: \"bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm hover:shadow-lg transition-all duration-300 border \".concat(colorClasses.border, \" \").concat(colorClasses.hover, \" cursor-pointer\"),\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            transition: {\n                                duration: 0.5,\n                                delay: index * 0.1\n                            },\n                            onClick: ()=>setActiveFeature(feature),\n                            whileHover: {\n                                y: -5\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 \".concat(colorClasses.bg, \" rounded-lg flex items-center justify-center mb-4\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: colorClasses.text,\n                                        children: feature.icon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                        lineNumber: 334,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                    lineNumber: 333,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white mb-2\",\n                                    children: feature.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                    lineNumber: 338,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300 mb-4\",\n                                    children: feature.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                    lineNumber: 341,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center text-sm font-medium text-primary-500 dark:text-primary-400\",\n                                    children: [\n                                        \"Ver detalhes\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"ml-1 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                            lineNumber: 346,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                    lineNumber: 344,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, feature.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                            lineNumber: 323,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                    lineNumber: 318,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n            lineNumber: 218,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Features, \"nAw/47mC1MxrYcjbU0YYaGlrYX0=\");\n_c = Features;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Features);\nvar _c;\n$RefreshReg$(_c, \"Features\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/Features.js\n"));

/***/ })

});