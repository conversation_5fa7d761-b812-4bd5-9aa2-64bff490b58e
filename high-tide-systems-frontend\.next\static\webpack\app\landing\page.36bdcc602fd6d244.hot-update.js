"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/landing/page",{

/***/ "(app-pages-browser)/./src/components/landing/Modules.js":
/*!*******************************************!*\
  !*** ./src/components/landing/Modules.js ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_Calendar_MessageSquare_Settings_ShieldCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,Calendar,MessageSquare,Settings,ShieldCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_Calendar_MessageSquare_Settings_ShieldCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,Calendar,MessageSquare,Settings,ShieldCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_Calendar_MessageSquare_Settings_ShieldCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,Calendar,MessageSquare,Settings,ShieldCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_Calendar_MessageSquare_Settings_ShieldCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,Calendar,MessageSquare,Settings,ShieldCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column-increasing.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_Calendar_MessageSquare_Settings_ShieldCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,Calendar,MessageSquare,Settings,ShieldCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_Calendar_MessageSquare_Settings_ShieldCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,Calendar,MessageSquare,Settings,ShieldCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst modules = [\n    {\n        id: 'scheduler',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_Calendar_MessageSquare_Settings_ShieldCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            size: 24\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n            lineNumber: 16,\n            columnNumber: 11\n        }, undefined),\n        title: 'Agendamento',\n        description: 'Gerencie consultas, compromissos e disponibilidade dos profissionais com um calendário intuitivo e completo.',\n        features: [\n            'Visualização diária, semanal e mensal',\n            'Agendamentos recorrentes',\n            'Detecção de conflitos',\n            'Horários de trabalho personalizáveis',\n            'Notificações automáticas'\n        ],\n        color: 'scheduler'\n    },\n    {\n        id: 'people',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_Calendar_MessageSquare_Settings_ShieldCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            size: 24\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n            lineNumber: 30,\n            columnNumber: 11\n        }, undefined),\n        title: 'Pessoas',\n        description: 'Cadastro completo de pacientes, profissionais e colaboradores com todas as informações necessárias.',\n        features: [\n            'Cadastro de pacientes e contatos',\n            'Histórico de atendimentos',\n            'Gestão de convênios',\n            'Documentos digitalizados',\n            'Relacionamentos familiares'\n        ],\n        color: 'people'\n    },\n    {\n        id: 'admin',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_Calendar_MessageSquare_Settings_ShieldCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            size: 24\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n            lineNumber: 45,\n            columnNumber: 11\n        }, undefined),\n        title: 'Administração',\n        description: 'Gerencie usuários, permissões, configurações e mantenha o controle total do sistema.',\n        features: [\n            'Gestão de usuários e permissões',\n            'Configurações do sistema',\n            'Logs de atividades',\n            'Backup de dados',\n            'Segurança e privacidade'\n        ],\n        color: 'admin'\n    },\n    {\n        id: 'reports',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_Calendar_MessageSquare_Settings_ShieldCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            size: 24\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n            lineNumber: 59,\n            columnNumber: 11\n        }, undefined),\n        title: 'Relatórios',\n        description: 'Dashboards e relatórios detalhados para análise de desempenho e tomada de decisões.',\n        features: [\n            'Dashboard interativo',\n            'Relatórios personalizáveis',\n            'Análise de ocupação',\n            'Indicadores de desempenho',\n            'Exportação em diversos formatos'\n        ],\n        color: 'reports'\n    },\n    {\n        id: 'chat',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_Calendar_MessageSquare_Settings_ShieldCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            size: 24\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n            lineNumber: 73,\n            columnNumber: 11\n        }, undefined),\n        title: 'Chat',\n        description: 'Sistema de comunicação integrado para facilitar a comunicação entre equipe e pacientes.',\n        features: [\n            'Chat em tempo real',\n            'Comunicação interna da equipe',\n            'Histórico de conversas',\n            'Notificações de mensagens',\n            'Interface intuitiva e responsiva'\n        ],\n        color: 'chat'\n    },\n    {\n        id: 'customization',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_Calendar_MessageSquare_Settings_ShieldCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            size: 24\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n            lineNumber: 87,\n            columnNumber: 11\n        }, undefined),\n        title: 'Personalização',\n        description: 'Adapte o sistema às necessidades específicas do seu negócio com opções de personalização.',\n        features: [\n            'Configurações personalizáveis',\n            'Campos customizados',\n            'Fluxos de trabalho adaptáveis',\n            'Interface configurável',\n            'Relatórios sob medida'\n        ],\n        color: 'customization'\n    }\n];\nconst Modules = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"modules\",\n        className: \"py-20 bg-gray-50 dark:bg-gray-800\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 md:px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    className: \"text-center max-w-3xl mx-auto mb-16\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\",\n                            children: \"M\\xf3dulos integrados\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 dark:text-gray-300\",\n                            children: \"Cada m\\xf3dulo foi desenvolvido para atender necessidades espec\\xedficas, mas todos trabalham em perfeita harmonia.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-16\",\n                    children: modules.map((module, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            className: \"flex flex-col \".concat(index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse', \" gap-8 items-center\"),\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            transition: {\n                                duration: 0.5\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white dark:bg-gray-900 rounded-xl shadow-lg overflow-hidden border border-gray-100 dark:border-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6 \".concat(module.id === 'scheduler' ? 'bg-purple-50 dark:bg-purple-900/20' : module.id === 'people' ? 'bg-amber-50 dark:bg-amber-900/20' : module.id === 'admin' ? 'bg-red-50 dark:bg-red-900/20' : module.id === 'reports' ? 'bg-blue-50 dark:bg-blue-900/20' : module.id === 'chat' ? 'bg-green-50 dark:bg-green-900/20' : module.id === 'customization' ? 'bg-orange-50 dark:bg-orange-900/20' : 'bg-gray-50 dark:bg-gray-900/20'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 rounded-lg \".concat(module.id === 'scheduler' ? 'bg-purple-100 dark:bg-purple-800/30' : module.id === 'people' ? 'bg-amber-100 dark:bg-amber-800/30' : module.id === 'admin' ? 'bg-red-100 dark:bg-red-800/30' : module.id === 'reports' ? 'bg-blue-100 dark:bg-blue-800/30' : module.id === 'chat' ? 'bg-green-100 dark:bg-green-800/30' : module.id === 'customization' ? 'bg-orange-100 dark:bg-orange-800/30' : 'bg-gray-100 dark:bg-gray-800/30', \" flex items-center justify-center mr-4\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: module.id === 'scheduler' ? 'text-purple-600 dark:text-purple-400' : module.id === 'people' ? 'text-amber-600 dark:text-amber-400' : module.id === 'admin' ? 'text-red-600 dark:text-red-400' : module.id === 'reports' ? 'text-blue-600 dark:text-blue-400' : module.id === 'chat' ? 'text-green-600 dark:text-green-400' : module.id === 'customization' ? 'text-orange-600 dark:text-orange-400' : 'text-gray-600 dark:text-gray-400',\n                                                                children: module.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                                lineNumber: 152,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl font-bold \".concat(module.id === 'scheduler' ? 'text-purple-800 dark:text-purple-300' : module.id === 'people' ? 'text-amber-800 dark:text-amber-300' : module.id === 'admin' ? 'text-red-800 dark:text-red-300' : module.id === 'reports' ? 'text-blue-800 dark:text-blue-300' : module.id === 'chat' ? 'text-green-800 dark:text-green-300' : module.id === 'customization' ? 'text-orange-800 dark:text-orange-300' : 'text-gray-800 dark:text-gray-300'),\n                                                            children: module.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                lineNumber: 133,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 dark:text-gray-300 mb-6\",\n                                                        children: module.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-3\",\n                                                        children: module.features.map((feature, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-1 mr-3 w-5 h-5 rounded-full flex items-center justify-center \".concat(module.id === 'scheduler' ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400' : module.id === 'people' ? 'bg-amber-100 dark:bg-amber-900/30 text-amber-600 dark:text-amber-400' : module.id === 'admin' ? 'bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400' : module.id === 'reports' ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400' : module.id === 'chat' ? 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400' : module.id === 'customization' ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400' : 'bg-gray-100 dark:bg-gray-900/30 text-gray-600 dark:text-gray-400'),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"h-3 w-3\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            fill: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                                                lineNumber: 194,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                                            lineNumber: 193,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                                        lineNumber: 184,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-700 dark:text-gray-300\",\n                                                                        children: feature\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                                        lineNumber: 197,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, i, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                                lineNumber: 183,\n                                                                columnNumber: 25\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                lineNumber: 177,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                        lineNumber: 132,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden border border-gray-200 dark:border-gray-700 p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"aspect-video bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mx-auto mb-4 \".concat(module.id === 'scheduler' ? 'text-purple-600 dark:text-purple-400' : module.id === 'people' ? 'text-amber-600 dark:text-amber-400' : module.id === 'financial' ? 'text-emerald-600 dark:text-emerald-400' : module.id === 'admin' ? 'text-red-600 dark:text-red-400' : 'text-blue-600 dark:text-blue-400'),\n                                                        children: module.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-xl font-semibold text-gray-900 dark:text-white mb-2\",\n                                                        children: [\n                                                            \"M\\xf3dulo de \",\n                                                            module.title\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 dark:text-gray-400\",\n                                                        children: \"Interface intuitiva e funcional para gerenciar todas as atividades relacionadas.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                lineNumber: 209,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                            lineNumber: 208,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                        lineNumber: 207,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, module.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Modules;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Modules);\nvar _c;\n$RefreshReg$(_c, \"Modules\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/Modules.js\n"));

/***/ })

});