"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/landing/page",{

/***/ "(app-pages-browser)/./src/components/landing/Hero.js":
/*!****************************************!*\
  !*** ./src/components/landing/Hero.js ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Pause_Play_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Pause,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Pause_Play_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Pause,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Pause_Play_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Pause,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Pause_Play_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Pause,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _utils_scrollUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/scrollUtils */ \"(app-pages-browser)/./src/utils/scrollUtils.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// Imagens do slider (substitua pelos caminhos reais das suas imagens)\n// Recomendação: Use imagens com resolução de 1920x1080 ou 1600x900 pixels\n// Formato recomendado: PNG ou WebP para melhor qualidade e transparência\n// Tamanho ideal: Entre 200KB e 500KB por imagem para carregamento rápido\nconst sliderImages = [\n    {\n        id: 1,\n        src: '/landing/hero/dashboard.png',\n        alt: 'Dashboard do sistema',\n        title: 'Dashboard Intuitivo',\n        description: 'Visualize todos os dados importantes da sua clínica em um único lugar'\n    },\n    {\n        id: 2,\n        src: '/landing/hero/calendar.png',\n        alt: 'Calendário de agendamentos',\n        title: 'Agendamento Simplificado',\n        description: 'Gerencie consultas e compromissos com facilidade e eficiência'\n    },\n    {\n        id: 3,\n        src: '/landing/hero/patients.png',\n        alt: 'Gestão de pacientes',\n        title: 'Gestão de Pacientes',\n        description: 'Mantenha todos os dados dos seus pacientes organizados e acessíveis'\n    }\n];\nconst Hero = ()=>{\n    _s();\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isHovering, setIsHovering] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const sliderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const autoplayRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Função para avançar para o próximo slide\n    const nextSlide = ()=>{\n        setCurrentSlide((prev)=>prev === sliderImages.length - 1 ? 0 : prev + 1);\n    };\n    // Função para voltar para o slide anterior\n    const prevSlide = ()=>{\n        setCurrentSlide((prev)=>prev === 0 ? sliderImages.length - 1 : prev - 1);\n    };\n    // Função para ir para um slide específico\n    const goToSlide = (index)=>{\n        setCurrentSlide(index);\n    };\n    // Configurar autoplay\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Hero.useEffect\": ()=>{\n            if (isPlaying && !isHovering) {\n                autoplayRef.current = setInterval({\n                    \"Hero.useEffect\": ()=>{\n                        nextSlide();\n                    }\n                }[\"Hero.useEffect\"], 3500); // Reduzido de 5000ms para 3500ms\n            }\n            return ({\n                \"Hero.useEffect\": ()=>{\n                    if (autoplayRef.current) {\n                        clearInterval(autoplayRef.current);\n                    }\n                }\n            })[\"Hero.useEffect\"];\n        }\n    }[\"Hero.useEffect\"], [\n        isPlaying,\n        isHovering,\n        currentSlide\n    ]);\n    // Manipuladores de eventos para hover\n    const handleMouseEnter = ()=>{\n        setIsHovering(true);\n    };\n    const handleMouseLeave = ()=>{\n        setIsHovering(false);\n    };\n    // Alternar reprodução automática\n    const toggleAutoplay = ()=>{\n        setIsPlaying(!isPlaying);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative pt-32 pb-20 md:pt-40 md:pb-28 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-gray-900 dark:to-gray-800 -z-10\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-300 via-primary-500 to-primary-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-24 right-10 w-72 h-72 bg-primary-300 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-36 left-10 w-72 h-72 bg-secondary-300 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-2000\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -bottom-8 left-20 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-4000\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 md:px-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row items-center gap-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: \"flex-1 text-center lg:text-left\",\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.5\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6 leading-tight\",\n                                    children: [\n                                        \"Gerencie seu neg\\xf3cio com \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-primary-500 dark:text-primary-400\",\n                                            children: \"efici\\xeancia\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                            lineNumber: 123,\n                                            columnNumber: 40\n                                        }, undefined),\n                                        \" e \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-primary-500 dark:text-primary-400\",\n                                            children: \"simplicidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                            lineNumber: 123,\n                                            columnNumber: 117\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto lg:mx-0\",\n                                    children: \"High Tide Systems \\xe9 a solu\\xe7\\xe3o completa para seu neg\\xf3cio, seja cl\\xednicas, consult\\xf3rios ou at\\xe9 seu pr\\xf3prio controle! Tudo em uma \\xfanica plataforma.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/subscription/signup\",\n                                            className: \"inline-flex items-center justify-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-primary-500 hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors\",\n                                            children: \"Come\\xe7ar agora\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#features\",\n                                            onClick: (e)=>{\n                                                e.preventDefault();\n                                                (0,_utils_scrollUtils__WEBPACK_IMPORTED_MODULE_3__.scrollToElement)('features', 80);\n                                            },\n                                            className: \"inline-flex items-center justify-center px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-base font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors cursor-pointer\",\n                                            children: \"Saiba mais\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: \"flex-1 relative\",\n                            initial: {\n                                opacity: 0,\n                                scale: 0.9\n                            },\n                            animate: {\n                                opacity: 1,\n                                scale: 1\n                            },\n                            transition: {\n                                duration: 0.5,\n                                delay: 0.2\n                            },\n                            onMouseEnter: handleMouseEnter,\n                            onMouseLeave: handleMouseLeave,\n                            ref: sliderRef,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden border border-gray-200 dark:border-gray-700 aspect-[16/10]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 bg-gray-100 dark:bg-gray-700 flex items-center px-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 bg-red-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 bg-yellow-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 bg-green-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-4 flex-1 h-5 bg-gray-200 dark:bg-gray-600 rounded-full px-2 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500 dark:text-gray-400 truncate\",\n                                                        children: \"hightide.site\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            style: {\n                                                height: \"calc(100% - 2rem)\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"overflow-hidden absolute inset-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative w-full h-full\",\n                                                        children: sliderImages.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.AnimatePresence, {\n                                                                children: currentSlide === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                                    className: \"absolute inset-0\",\n                                                                    initial: {\n                                                                        opacity: 0,\n                                                                        x: 20\n                                                                    },\n                                                                    animate: {\n                                                                        opacity: 1,\n                                                                        x: 0\n                                                                    },\n                                                                    exit: {\n                                                                        opacity: 0,\n                                                                        x: -20\n                                                                    },\n                                                                    transition: {\n                                                                        duration: 0.5\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative h-full\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 bg-gradient-to-t from-black/70 to-transparent z-10\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                                                lineNumber: 186,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                src: image.src,\n                                                                                alt: image.alt,\n                                                                                className: \"absolute inset-0 w-full h-full object-cover object-center\",\n                                                                                style: {\n                                                                                    imageRendering: 'crisp-edges',\n                                                                                    maxWidth: '100%',\n                                                                                    maxHeight: '100%'\n                                                                                },\n                                                                                loading: \"eager\",\n                                                                                onError: (e)=>{\n                                                                                    e.target.onerror = null;\n                                                                                    e.target.src = '';\n                                                                                    e.target.parentElement.innerHTML = '\\n                                    <div class=\"absolute inset-0 w-full h-full flex items-center justify-center bg-gray-200 dark:bg-gray-700\">\\n                                      <div class=\"text-center p-8\">\\n                                        <div class=\"text-primary-500 dark:text-primary-400 mx-auto mb-4\">\\n                                          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\\n                                            <rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"></rect>\\n                                            <circle cx=\"8.5\" cy=\"8.5\" r=\"1.5\"></circle>\\n                                            <polyline points=\"21 15 16 10 5 21\"></polyline>\\n                                          </svg>\\n                                        </div>\\n                                        <p class=\"text-gray-500 dark:text-gray-400\">Imagem do sistema</p>\\n                                      </div>\\n                                    </div>\\n                                  ';\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                                                lineNumber: 187,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute bottom-0 left-0 p-8 right-0 z-20\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                        className: \"text-xl font-bold text-white mb-2\",\n                                                                                        children: image.title\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                                                        lineNumber: 217,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-gray-200\",\n                                                                                        children: image.description\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                                                        lineNumber: 218,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                                                lineNumber: 216,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                                        lineNumber: 185,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                                    lineNumber: 178,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, image.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                                lineNumber: 176,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center text-white hover:bg-white/30 transition-colors z-20\",\n                                                    onClick: prevSlide,\n                                                    \"aria-label\": \"Slide anterior\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Pause_Play_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        size: 20\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center text-white hover:bg-white/30 transition-colors z-20\",\n                                                    onClick: nextSlide,\n                                                    \"aria-label\": \"Pr\\xf3ximo slide\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Pause_Play_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        size: 20\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"absolute right-4 bottom-4 w-8 h-8 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center text-white hover:bg-white/30 transition-colors z-20\",\n                                                    onClick: toggleAutoplay,\n                                                    \"aria-label\": isPlaying ? \"Pausar apresentação\" : \"Iniciar apresentação\",\n                                                    children: isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Pause_Play_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 32\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Pause_Play_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 54\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2 z-20\",\n                                                    children: sliderImages.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"w-2 h-2 rounded-full transition-all \".concat(currentSlide === index ? \"w-6 bg-white\" : \"bg-white/50 hover:bg-white/80\"),\n                                                            onClick: ()=>goToSlide(index),\n                                                            \"aria-label\": \"Ir para slide \".concat(index + 1)\n                                                        }, index, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 21\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -z-10 -right-4 -bottom-4 w-full h-full bg-gradient-to-br from-primary-200 to-primary-400 dark:from-primary-700 dark:to-primary-900 rounded-2xl\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Hero, \"T5GN1rk4I4GwrSKS0Pn4jI7oKJ0=\");\n_c = Hero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Hero);\nvar _c;\n$RefreshReg$(_c, \"Hero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/Hero.js\n"));

/***/ })

});