'use client';

import { motion } from 'framer-motion';
import { Users, Target, Lightbulb, Heart } from 'lucide-react';

const About = () => {
  return (
    <section id="about" className="py-16 bg-white dark:bg-gray-900">
      <div className="container mx-auto px-4 md:px-6">
        <motion.div
          className="text-center max-w-3xl mx-auto mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Sobre nós
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300">
            Conheça a história e a missão por trás do High Tide Systems
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
          {/* <PERSON><PERSON> */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
              Nossa História
            </h3>
            <div className="space-y-4 text-gray-600 dark:text-gray-300">
              <p>
                Somos pessoas que trabalharam na área de sistemas de empresas, e por não encontrar um sistema de fácil interação, completo e com personalização própria, decidimos criar um.
              </p>
              <p>
                Somos formados em análise e desenvolvimento de sistemas, e já havíamos criado um protótipo para uma empresa clínica onde trabalhávamos, que lida com atendimento, gestão de clientes e funcionários.
              </p>
              <p>
                Nossa experiência prática nos mostrou as dificuldades reais que empresas enfrentam no dia a dia, desde a complexidade de sistemas existentes até a falta de personalização adequada para diferentes tipos de negócio.
              </p>
            </div>
          </motion.div>

          {/* Nossa Missão */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
              Nossa Missão
            </h3>
            <div className="space-y-4 text-gray-600 dark:text-gray-300">
              <p>
                Nosso plano é criar um sistema único, envolvendo financeiro, RH, ERP, faturamento, estoque e compras, tudo de uma maneira amigável para qualquer usuário.
              </p>
              <p>
                Queremos atender desde microempreendedores que trabalham sozinhos até grandes empresas, oferecendo uma solução escalável e intuitiva que cresce junto com o negócio.
              </p>
              <p>
                Acreditamos que tecnologia deve simplificar, não complicar. Por isso, desenvolvemos cada funcionalidade pensando na experiência do usuário e na eficiência operacional.
              </p>
            </div>
          </motion.div>
        </div>

        {/* Nossos Valores */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <div className="text-center">
            <div className="w-16 h-16 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
              <Users className="h-8 w-8 text-orange-600 dark:text-orange-400" />
            </div>
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Foco no Usuário
            </h4>
            <p className="text-gray-600 dark:text-gray-300 text-sm">
              Desenvolvemos pensando sempre na experiência e facilidade de uso para todos os tipos de usuário.
            </p>
          </div>

          <div className="text-center">
            <div className="w-16 h-16 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
              <Lightbulb className="h-8 w-8 text-orange-600 dark:text-orange-400" />
            </div>
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Inovação
            </h4>
            <p className="text-gray-600 dark:text-gray-300 text-sm">
              Buscamos constantemente novas formas de melhorar e simplificar a gestão empresarial.
            </p>
          </div>

          <div className="text-center">
            <div className="w-16 h-16 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
              <Target className="h-8 w-8 text-orange-600 dark:text-orange-400" />
            </div>
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Eficiência
            </h4>
            <p className="text-gray-600 dark:text-gray-300 text-sm">
              Cada funcionalidade é pensada para otimizar processos e economizar tempo valioso.
            </p>
          </div>

          <div className="text-center">
            <div className="w-16 h-16 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
              <Heart className="h-8 w-8 text-orange-600 dark:text-orange-400" />
            </div>
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Compromisso
            </h4>
            <p className="text-gray-600 dark:text-gray-300 text-sm">
              Estamos comprometidos com o sucesso dos nossos clientes e o crescimento dos seus negócios.
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default About;
