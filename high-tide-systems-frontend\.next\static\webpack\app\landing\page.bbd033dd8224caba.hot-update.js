"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/landing/page",{

/***/ "(app-pages-browser)/./src/components/landing/Features.js":
/*!********************************************!*\
  !*** ./src/components/landing/Features.js ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart4,Bell,Calendar,CheckCircle,Clock,CreditCard,MessageSquare,Shield,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart4,Bell,Calendar,CheckCircle,Clock,CreditCard,MessageSquare,Shield,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart4,Bell,Calendar,CheckCircle,Clock,CreditCard,MessageSquare,Shield,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart4,Bell,Calendar,CheckCircle,Clock,CreditCard,MessageSquare,Shield,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart4,Bell,Calendar,CheckCircle,Clock,CreditCard,MessageSquare,Shield,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column-increasing.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart4,Bell,Calendar,CheckCircle,Clock,CreditCard,MessageSquare,Shield,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart4,Bell,Calendar,CheckCircle,Clock,CreditCard,MessageSquare,Shield,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart4,Bell,Calendar,CheckCircle,Clock,CreditCard,MessageSquare,Shield,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart4,Bell,Calendar,CheckCircle,Clock,CreditCard,MessageSquare,Shield,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart4,Bell,Calendar,CheckCircle,Clock,CreditCard,MessageSquare,Shield,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst features = [\n    {\n        id: 'scheduling',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n            lineNumber: 22,\n            columnNumber: 11\n        }, undefined),\n        title: 'Agendamento Inteligente',\n        description: 'Gerencie consultas e compromissos com facilidade. Visualização em calendário diário, semanal e mensal com detecção automática de conflitos.',\n        color: 'purple',\n        benefits: [\n            'Redução de 70% no tempo gasto com agendamentos',\n            'Eliminação de conflitos de horários',\n            'Visualização clara da agenda de cada profissional',\n            'Agendamentos recorrentes com um clique',\n            'Confirmação automática por e-mail e SMS'\n        ],\n        image: 'landing/features/scheduling.png'\n    },\n    {\n        id: 'patients',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n            lineNumber: 37,\n            columnNumber: 11\n        }, undefined),\n        title: 'Gestão de Pacientes',\n        description: 'Cadastro completo de pacientes com histórico médico, documentos, convênios e relacionamentos familiares.',\n        color: 'amber',\n        benefits: [\n            'Prontuário eletrônico completo e seguro',\n            'Histórico de atendimentos e evolução',\n            'Gestão de documentos digitalizados',\n            'Controle de convênios e planos de saúde',\n            'Relacionamentos familiares e contatos de emergência'\n        ],\n        image: '/features/patients.png'\n    },\n    {\n        id: 'working-hours',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n            lineNumber: 52,\n            columnNumber: 11\n        }, undefined),\n        title: 'Horários de Trabalho',\n        description: 'Configure a disponibilidade de cada profissional, incluindo intervalos, folgas e feriados para otimizar o agendamento.',\n        color: 'blue',\n        benefits: [\n            'Configuração flexível de horários por profissional',\n            'Definição de intervalos e pausas',\n            'Calendário de folgas e feriados',\n            'Bloqueio de horários para reuniões e eventos',\n            'Visualização da ocupação em tempo real'\n        ],\n        image: '/features/working-hours.png'\n    },\n    {\n        id: 'notifications',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n            lineNumber: 67,\n            columnNumber: 11\n        }, undefined),\n        title: 'Notificações Automáticas',\n        description: 'Lembretes de consultas por e-mail e sistema para reduzir faltas e aumentar a satisfação dos pacientes.',\n        color: 'red',\n        benefits: [\n            'Redução de até 40% nas faltas de pacientes',\n            'Lembretes personalizáveis por tipo de atendimento',\n            'Confirmação de presença com um clique',\n            'Notificações para a equipe sobre alterações',\n            'Alertas de aniversários e datas importantes'\n        ],\n        image: '/features/notifications.png'\n    },\n    {\n        id: 'reports',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n            lineNumber: 83,\n            columnNumber: 11\n        }, undefined),\n        title: 'Relatórios e Dashboards',\n        description: 'Análises detalhadas de desempenho, ocupação, faturamento e outros indicadores importantes para sua clínica.',\n        color: 'indigo',\n        benefits: [\n            'Dashboard interativo com KPIs em tempo real',\n            'Relatórios personalizáveis por período',\n            'Análise de ocupação e produtividade',\n            'Indicadores de desempenho e produtividade',\n            'Exportação em diversos formatos (PDF, Excel, CSV)'\n        ],\n        image: '/features/reports.png'\n    },\n    {\n        id: 'chat',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n            lineNumber: 98,\n            columnNumber: 11\n        }, undefined),\n        title: 'Chat Integrado',\n        description: 'Comunicação interna entre profissionais e equipe administrativa para agilizar processos e melhorar a colaboração.',\n        color: 'orange',\n        benefits: [\n            'Comunicação em tempo real entre a equipe',\n            'Grupos por departamento ou função',\n            'Compartilhamento de arquivos e imagens',\n            'Histórico completo de conversas',\n            'Notificações de mensagens importantes'\n        ],\n        image: '/features/chat.png'\n    },\n    {\n        id: 'security',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n            lineNumber: 113,\n            columnNumber: 11\n        }, undefined),\n        title: 'Segurança e Privacidade',\n        description: 'Controle de acesso por perfil, registro de atividades e conformidade com LGPD para proteger dados sensíveis.',\n        color: 'rose',\n        benefits: [\n            'Conformidade total com a LGPD',\n            'Controle de acesso granular por perfil',\n            'Registro detalhado de todas as atividades',\n            'Criptografia de dados sensíveis',\n            'Backups automáticos e redundantes'\n        ],\n        image: '/features/security.png'\n    },\n    {\n        id: 'patient-portal',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n            lineNumber: 128,\n            columnNumber: 11\n        }, undefined),\n        title: 'Acesso para Pacientes',\n        description: 'Portal web para pacientes visualizarem agendamentos, histórico e atualizarem dados pessoais.',\n        color: 'cyan',\n        benefits: [\n            'Visualização de agendamentos via web',\n            'Consulta de histórico de atendimentos',\n            'Acesso a informações de consultas',\n            'Atualização de dados cadastrais',\n            'Comunicação direta com a clínica'\n        ],\n        image: '/features/patient-portal.png'\n    }\n];\nconst Features = ()=>{\n    _s();\n    const [activeFeature, setActiveFeature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const getColorClasses = (color, isActive)=>{\n        const colorMap = {\n            purple: {\n                bg: isActive ? 'bg-purple-500' : 'bg-purple-100 dark:bg-purple-900/30',\n                text: isActive ? 'text-white' : 'text-purple-500 dark:text-purple-400',\n                border: 'border-purple-200 dark:border-purple-800',\n                hover: 'hover:bg-purple-50 dark:hover:bg-purple-900/40',\n                shadow: 'shadow-purple-100 dark:shadow-purple-900/20'\n            },\n            amber: {\n                bg: isActive ? 'bg-amber-500' : 'bg-amber-100 dark:bg-amber-900/30',\n                text: isActive ? 'text-white' : 'text-amber-500 dark:text-amber-400',\n                border: 'border-amber-200 dark:border-amber-800',\n                hover: 'hover:bg-amber-50 dark:hover:bg-amber-900/40',\n                shadow: 'shadow-amber-100 dark:shadow-amber-900/20'\n            },\n            blue: {\n                bg: isActive ? 'bg-blue-500' : 'bg-blue-100 dark:bg-blue-900/30',\n                text: isActive ? 'text-white' : 'text-blue-500 dark:text-blue-400',\n                border: 'border-blue-200 dark:border-blue-800',\n                hover: 'hover:bg-blue-50 dark:hover:bg-blue-900/40',\n                shadow: 'shadow-blue-100 dark:shadow-blue-900/20'\n            },\n            red: {\n                bg: isActive ? 'bg-red-500' : 'bg-red-100 dark:bg-red-900/30',\n                text: isActive ? 'text-white' : 'text-red-500 dark:text-red-400',\n                border: 'border-red-200 dark:border-red-800',\n                hover: 'hover:bg-red-50 dark:hover:bg-red-900/40',\n                shadow: 'shadow-red-100 dark:shadow-red-900/20'\n            },\n            emerald: {\n                bg: isActive ? 'bg-emerald-500' : 'bg-emerald-100 dark:bg-emerald-900/30',\n                text: isActive ? 'text-white' : 'text-emerald-500 dark:text-emerald-400',\n                border: 'border-emerald-200 dark:border-emerald-800',\n                hover: 'hover:bg-emerald-50 dark:hover:bg-emerald-900/40',\n                shadow: 'shadow-emerald-100 dark:shadow-emerald-900/20'\n            },\n            indigo: {\n                bg: isActive ? 'bg-indigo-500' : 'bg-indigo-100 dark:bg-indigo-900/30',\n                text: isActive ? 'text-white' : 'text-indigo-500 dark:text-indigo-400',\n                border: 'border-indigo-200 dark:border-indigo-800',\n                hover: 'hover:bg-indigo-50 dark:hover:bg-indigo-900/40',\n                shadow: 'shadow-indigo-100 dark:shadow-indigo-900/20'\n            },\n            orange: {\n                bg: isActive ? 'bg-orange-500' : 'bg-orange-100 dark:bg-orange-900/30',\n                text: isActive ? 'text-white' : 'text-orange-500 dark:text-orange-400',\n                border: 'border-orange-200 dark:border-orange-800',\n                hover: 'hover:bg-orange-50 dark:hover:bg-orange-900/40',\n                shadow: 'shadow-orange-100 dark:shadow-orange-900/20'\n            },\n            rose: {\n                bg: isActive ? 'bg-rose-500' : 'bg-rose-100 dark:bg-rose-900/30',\n                text: isActive ? 'text-white' : 'text-rose-500 dark:text-rose-400',\n                border: 'border-rose-200 dark:border-rose-800',\n                hover: 'hover:bg-rose-50 dark:hover:bg-rose-900/40',\n                shadow: 'shadow-rose-100 dark:shadow-rose-900/20'\n            },\n            cyan: {\n                bg: isActive ? 'bg-cyan-500' : 'bg-cyan-100 dark:bg-cyan-900/30',\n                text: isActive ? 'text-white' : 'text-cyan-500 dark:text-cyan-400',\n                border: 'border-cyan-200 dark:border-cyan-800',\n                hover: 'hover:bg-cyan-50 dark:hover:bg-cyan-900/40',\n                shadow: 'shadow-cyan-100 dark:shadow-cyan-900/20'\n            }\n        };\n        return colorMap[color] || colorMap.purple;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"features\",\n        className: \"py-20 bg-white dark:bg-gray-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 md:px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                    className: \"text-center max-w-3xl mx-auto mb-16\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\",\n                            children: \"Recursos completos para sua cl\\xednica\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 dark:text-gray-300\",\n                            children: \"Tudo o que voc\\xea precisa para gerenciar sua cl\\xednica ou consult\\xf3rio em uma \\xfanica plataforma intuitiva e poderosa.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                    lineNumber: 219,\n                    columnNumber: 9\n                }, undefined),\n                activeFeature && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                    className: \"mb-16 bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden border border-gray-100 dark:border-gray-700\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 dark:bg-gray-900 p-8 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative w-full h-64 md:h-80 lg:h-full rounded-xl overflow-hidden bg-gray-200 dark:bg-gray-700 flex items-center justify-center\",\n                                    children: activeFeature.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: activeFeature.image,\n                                        alt: activeFeature.title,\n                                        className: \"w-full h-full object-cover\",\n                                        onError: (e)=>{\n                                            e.target.onerror = null;\n                                            e.target.style.display = 'none';\n                                            e.target.parentElement.innerHTML = '\\n                          <div class=\"text-center p-8\">\\n                            <div class=\"'.concat(getColorClasses(activeFeature.color, false).text, ' mx-auto mb-4\">\\n                              ').concat(activeFeature.icon.props.children, '\\n                            </div>\\n                            <p class=\"text-gray-500 dark:text-gray-400\">Visualiza\\xe7\\xe3o do recurso</p>\\n                          </div>\\n                        ');\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                        lineNumber: 247,\n                                        columnNumber: 21\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center p-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\".concat(getColorClasses(activeFeature.color, false).text, \" mx-auto mb-4\"),\n                                                children: activeFeature.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                                lineNumber: 266,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 dark:text-gray-400\",\n                                                children: \"Visualiza\\xe7\\xe3o do recurso\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                                lineNumber: 269,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                        lineNumber: 265,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                    lineNumber: 245,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                lineNumber: 244,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-lg \".concat(getColorClasses(activeFeature.color, true).bg, \" flex items-center justify-center mr-4\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white\",\n                                                    children: activeFeature.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                                lineNumber: 278,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                children: activeFeature.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                                lineNumber: 283,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                        lineNumber: 277,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-300 mb-6\",\n                                        children: activeFeature.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white mb-4\",\n                                        children: \"Principais benef\\xedcios:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                        lineNumber: 292,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3 mb-8\",\n                                        children: activeFeature.benefits.map((benefit, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5 mt-0.5 mr-3 \".concat(getColorClasses(activeFeature.color, false).text)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 dark:text-gray-300\",\n                                                        children: benefit\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, i, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                                lineNumber: 298,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                        lineNumber: 296,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveFeature(null),\n                                        className: \"text-primary-500 dark:text-primary-400 hover:underline flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1 rotate-180\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                                lineNumber: 309,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Voltar para todos os recursos\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                        lineNumber: 305,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                lineNumber: 276,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                        lineNumber: 242,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                    lineNumber: 236,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 \".concat(activeFeature ? 'hidden lg:grid' : ''),\n                    children: features.map((feature, index)=>{\n                        const colorClasses = getColorClasses(feature.color, false);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                            className: \"bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm hover:shadow-lg transition-all duration-300 border \".concat(colorClasses.border, \" \").concat(colorClasses.hover, \" cursor-pointer\"),\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            transition: {\n                                duration: 0.5,\n                                delay: index * 0.1\n                            },\n                            onClick: ()=>setActiveFeature(feature),\n                            whileHover: {\n                                y: -5\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 \".concat(colorClasses.bg, \" rounded-lg flex items-center justify-center mb-4\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: colorClasses.text,\n                                        children: feature.icon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                        lineNumber: 334,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                    lineNumber: 333,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white mb-2\",\n                                    children: feature.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                    lineNumber: 338,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300 mb-4\",\n                                    children: feature.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                    lineNumber: 341,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center text-sm font-medium text-primary-500 dark:text-primary-400\",\n                                    children: [\n                                        \"Ver detalhes\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart4_Bell_Calendar_CheckCircle_Clock_CreditCard_MessageSquare_Shield_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"ml-1 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                            lineNumber: 346,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                                    lineNumber: 344,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, feature.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                            lineNumber: 323,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n                    lineNumber: 318,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n            lineNumber: 218,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Features.js\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Features, \"nAw/47mC1MxrYcjbU0YYaGlrYX0=\");\n_c = Features;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Features);\nvar _c;\n$RefreshReg$(_c, \"Features\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/Features.js\n"));

/***/ })

});