'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import { ChevronLeft, ChevronRight, Play, Pause } from 'lucide-react';
import { scrollToElement } from '@/utils/scrollUtils';

// Imagens do slider (substitua pelos caminhos reais das suas imagens)
// Recomendação: Use imagens com resolução de 1920x1080 ou 1600x900 pixels
// Formato recomendado: PNG ou WebP para melhor qualidade e transparência
// Tamanho ideal: Entre 200KB e 500KB por imagem para carregamento rápido
const sliderImages = [
  {
    id: 1,
    src: '/landing/hero/dashboard.png',
    alt: 'Dashboard do sistema',
    title: 'Dashboard Intuitivo',
    description: 'Visualize todos os dados importantes da sua clínica em um único lugar'
  },
  {
    id: 2,
    src: '/landing/hero/calendar.png',
    alt: 'Calendário de agendamentos',
    title: 'Agendamento Simplificado',
    description: 'Gerencie consultas e compromissos com facilidade e eficiência'
  },
  {
    id: 3,
    src: '/landing/hero/patients.png',
    alt: 'Gestão de pacientes',
    title: 'Gestão de Pacientes',
    description: 'Mantenha todos os dados dos seus pacientes organizados e acessíveis'
  },
  // {
  //   id: 4,
  //   src: '/screenshots/financial.jpg',
  //   alt: 'Relatórios financeiros',
  //   title: 'Controle Financeiro',
  //   description: 'Acompanhe receitas, despesas e lucratividade da sua clínica'
  // },
  // {
  //   id: 5,
  //   src: '/screenshots/mobile.jpg',
  //   alt: 'Versão mobile',
  //   title: 'Acesso em Qualquer Lugar',
  //   description: 'Use o sistema em qualquer dispositivo, a qualquer momento'
  // }
];

const Hero = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);
  const [isHovering, setIsHovering] = useState(false);
  const sliderRef = useRef(null);
  const autoplayRef = useRef(null);

  // Função para avançar para o próximo slide
  const nextSlide = () => {
    setCurrentSlide((prev) => (prev === sliderImages.length - 1 ? 0 : prev + 1));
  };

  // Função para voltar para o slide anterior
  const prevSlide = () => {
    setCurrentSlide((prev) => (prev === 0 ? sliderImages.length - 1 : prev - 1));
  };

  // Função para ir para um slide específico
  const goToSlide = (index) => {
    setCurrentSlide(index);
  };

  // Configurar autoplay
  useEffect(() => {
    if (isPlaying && !isHovering) {
      autoplayRef.current = setInterval(() => {
        nextSlide();
      }, 3500); // Reduzido de 5000ms para 3500ms
    }

    return () => {
      if (autoplayRef.current) {
        clearInterval(autoplayRef.current);
      }
    };
  }, [isPlaying, isHovering, currentSlide]);

  // Manipuladores de eventos para hover
  const handleMouseEnter = () => {
    setIsHovering(true);
  };

  const handleMouseLeave = () => {
    setIsHovering(false);
  };

  // Alternar reprodução automática
  const toggleAutoplay = () => {
    setIsPlaying(!isPlaying);
  };

  return (
    <section className="relative pt-32 pb-20 md:pt-40 md:pb-28 overflow-hidden">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-gray-900 dark:to-gray-800 -z-10" />

      {/* Decorative elements */}
      <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-300 via-primary-500 to-primary-300" />
      <div className="absolute top-24 right-10 w-72 h-72 bg-primary-300 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob" />
      <div className="absolute top-36 left-10 w-72 h-72 bg-secondary-300 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-2000" />
      <div className="absolute -bottom-8 left-20 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-4000" />

      <div className="container mx-auto px-4 md:px-6">
        <div className="flex flex-col lg:flex-row items-center gap-12">
          {/* Text content */}
          <motion.div
            className="flex-1 text-center lg:text-left"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6 leading-tight">
              Gerencie seu negócio com <span className="text-primary-500 dark:text-primary-400">eficiência</span> e <span className="text-primary-500 dark:text-primary-400">simplicidade</span>
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto lg:mx-0">
              High Tide Systems é a solução completa para seu negócio, seja clínicas, consultórios ou até seu próprio controle! Tudo em uma única plataforma.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Link
                href="/subscription/signup"
                className="inline-flex items-center justify-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-primary-500 hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
              >
                Começar agora
              </Link>
              <a
                href="#features"
                onClick={(e) => {
                  e.preventDefault();
                  scrollToElement('features', 80);
                }}
                className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-base font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors cursor-pointer"
              >
                Saiba mais
              </a>
            </div>
          </motion.div>

          {/* Image Slider */}
          <motion.div
            className="flex-1 relative"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            ref={sliderRef}
          >
            <div className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden border border-gray-200 dark:border-gray-700 aspect-[16/10]">
              {/* Browser-like top bar */}
              <div className="h-8 bg-gray-100 dark:bg-gray-700 flex items-center px-4">
                <div className="flex space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                </div>
                <div className="ml-4 flex-1 h-5 bg-gray-200 dark:bg-gray-600 rounded-full px-2 flex items-center justify-center">
                  <div className="text-xs text-gray-500 dark:text-gray-400 truncate">hightide.site</div>
                </div>
              </div>

              {/* Slider content */}
              <div className="relative" style={{ height: "calc(100% - 2rem)" }}>
                <div className="overflow-hidden absolute inset-0">
                  <div className="relative w-full h-full">
                    {sliderImages.map((image, index) => (
                      <AnimatePresence key={image.id}>
                        {currentSlide === index && (
                          <motion.div
                            className="absolute inset-0"
                            initial={{ opacity: 0, x: 20 }}
                            animate={{ opacity: 1, x: 0 }}
                            exit={{ opacity: 0, x: -20 }}
                            transition={{ duration: 0.5 }}
                          >
                            <div className="relative h-full">
                              <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent z-10"></div>
                              <img
                                src={image.src}
                                alt={image.alt}
                                className="absolute inset-0 w-full h-full object-cover object-center"
                                style={{
                                  imageRendering: 'crisp-edges',
                                  maxWidth: '100%',
                                  maxHeight: '100%'
                                }}
                                loading="eager"
                                onError={(e) => {
                                  e.target.onerror = null;
                                  e.target.src = '';
                                  e.target.parentElement.innerHTML = `
                                    <div class="absolute inset-0 w-full h-full flex items-center justify-center bg-gray-200 dark:bg-gray-700">
                                      <div class="text-center p-8">
                                        <div class="text-primary-500 dark:text-primary-400 mx-auto mb-4">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                            <circle cx="8.5" cy="8.5" r="1.5"></circle>
                                            <polyline points="21 15 16 10 5 21"></polyline>
                                          </svg>
                                        </div>
                                        <p class="text-gray-500 dark:text-gray-400">Imagem do sistema</p>
                                      </div>
                                    </div>
                                  `;
                                }}
                              />
                              <div className="absolute bottom-0 left-0 p-8 right-0 z-20">
                                  <h3 className="text-xl font-bold text-white mb-2">{image.title}</h3>
                                  <p className="text-gray-200">{image.description}</p>
                              </div>
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    ))}
                  </div>
                </div>

                {/* Navigation arrows */}
                <button
                  className="absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center text-white hover:bg-white/30 transition-colors z-20"
                  onClick={prevSlide}
                  aria-label="Slide anterior"
                >
                  <ChevronLeft size={20} />
                </button>
                <button
                  className="absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center text-white hover:bg-white/30 transition-colors z-20"
                  onClick={nextSlide}
                  aria-label="Próximo slide"
                >
                  <ChevronRight size={20} />
                </button>

                {/* Autoplay control */}
                <button
                  className="absolute right-4 bottom-4 w-8 h-8 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center text-white hover:bg-white/30 transition-colors z-20"
                  onClick={toggleAutoplay}
                  aria-label={isPlaying ? "Pausar apresentação" : "Iniciar apresentação"}
                >
                  {isPlaying ? <Pause size={16} /> : <Play size={16} />}
                </button>

                {/* Dots navigation */}
                <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2 z-20">
                  {sliderImages.map((_, index) => (
                    <button
                      key={index}
                      className={`w-2 h-2 rounded-full transition-all ${
                        currentSlide === index
                          ? "w-6 bg-white"
                          : "bg-white/50 hover:bg-white/80"
                      }`}
                      onClick={() => goToSlide(index)}
                      aria-label={`Ir para slide ${index + 1}`}
                    />
                  ))}
                </div>
              </div>
            </div>

            {/* Decorative elements */}
            <div className="absolute -z-10 -right-4 -bottom-4 w-full h-full bg-gradient-to-br from-primary-200 to-primary-400 dark:from-primary-700 dark:to-primary-900 rounded-2xl"></div>
          </motion.div>
        </div>


      </div>
    </section>
  );
};

export default Hero;
