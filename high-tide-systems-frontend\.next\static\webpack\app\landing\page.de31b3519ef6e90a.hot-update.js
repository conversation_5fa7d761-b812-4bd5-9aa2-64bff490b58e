"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/landing/page",{

/***/ "(app-pages-browser)/./src/components/landing/FAQ.js":
/*!***************************************!*\
  !*** ./src/components/landing/FAQ.js ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Clock_CreditCard_HelpCircle_Settings_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Clock,CreditCard,HelpCircle,Settings,Shield,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Clock_CreditCard_HelpCircle_Settings_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Clock,CreditCard,HelpCircle,Settings,Shield,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Clock_CreditCard_HelpCircle_Settings_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Clock,CreditCard,HelpCircle,Settings,Shield,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Clock_CreditCard_HelpCircle_Settings_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Clock,CreditCard,HelpCircle,Settings,Shield,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Clock_CreditCard_HelpCircle_Settings_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Clock,CreditCard,HelpCircle,Settings,Shield,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Clock_CreditCard_HelpCircle_Settings_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Clock,CreditCard,HelpCircle,Settings,Shield,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Clock_CreditCard_HelpCircle_Settings_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Clock,CreditCard,HelpCircle,Settings,Shield,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Clock_CreditCard_HelpCircle_Settings_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Clock,CreditCard,HelpCircle,Settings,Shield,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _utils_scrollUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/scrollUtils */ \"(app-pages-browser)/./src/utils/scrollUtils.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst faqCategories = [\n    {\n        id: 'general',\n        name: 'Geral',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_CreditCard_HelpCircle_Settings_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n            lineNumber: 12,\n            columnNumber: 11\n        }, undefined),\n        color: 'text-primary-500 dark:text-primary-400'\n    },\n    {\n        id: 'security',\n        name: 'Segurança',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_CreditCard_HelpCircle_Settings_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n            lineNumber: 18,\n            columnNumber: 11\n        }, undefined),\n        color: 'text-red-500 dark:text-red-400'\n    },\n    {\n        id: 'implementation',\n        name: 'Implementação',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_CreditCard_HelpCircle_Settings_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n            lineNumber: 24,\n            columnNumber: 11\n        }, undefined),\n        color: 'text-amber-500 dark:text-amber-400'\n    },\n    {\n        id: 'technical',\n        name: 'Técnico',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_CreditCard_HelpCircle_Settings_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n            lineNumber: 30,\n            columnNumber: 11\n        }, undefined),\n        color: 'text-purple-500 dark:text-purple-400'\n    },\n    {\n        id: 'customization',\n        name: 'Personalização',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_CreditCard_HelpCircle_Settings_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n            lineNumber: 36,\n            columnNumber: 11\n        }, undefined),\n        color: 'text-emerald-500 dark:text-emerald-400'\n    },\n    {\n        id: 'pricing',\n        name: 'Preços',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_CreditCard_HelpCircle_Settings_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n            lineNumber: 42,\n            columnNumber: 11\n        }, undefined),\n        color: 'text-blue-500 dark:text-blue-400'\n    }\n];\nconst faqs = [\n    {\n        id: 1,\n        category: 'general',\n        question: \"Como o High Tide Systems pode ajudar minha clínica?\",\n        answer: \"O High Tide Systems oferece uma solução completa para gestão de clínicas e consultórios, integrando agendamentos, cadastro de pacientes, financeiro, relatórios e muito mais. Nossa plataforma ajuda a reduzir o tempo gasto com tarefas administrativas, minimizar erros, melhorar a comunicação entre a equipe e proporcionar uma experiência superior aos pacientes.\"\n    },\n    {\n        id: 2,\n        category: 'implementation',\n        question: \"Quanto tempo leva para implementar o sistema?\",\n        answer: \"A implementação do High Tide Systems é rápida e simples. Após a contratação, você pode começar a usar o sistema em apenas 24 horas. Oferecemos treinamento completo para sua equipe e suporte durante todo o processo de implementação. A maioria das clínicas consegue estar totalmente operacional com o sistema em menos de uma semana.\"\n    },\n    {\n        id: 3,\n        category: 'security',\n        question: \"O sistema é seguro e atende às normas de proteção de dados?\",\n        answer: \"Sim, o High Tide Systems foi desenvolvido com foco na segurança e privacidade dos dados. Estamos em conformidade com a LGPD (Lei Geral de Proteção de Dados) e utilizamos tecnologias avançadas de criptografia e proteção de dados. Realizamos backups regulares e implementamos controles de acesso rigorosos para garantir a segurança das informações.\"\n    },\n    {\n        id: 4,\n        category: 'technical',\n        question: \"Posso acessar o sistema de qualquer dispositivo?\",\n        answer: \"Sim, o High Tide Systems é uma plataforma baseada em nuvem, o que significa que você pode acessá-la de qualquer dispositivo com conexão à internet, incluindo computadores, tablets e smartphones. Nossa interface é responsiva e se adapta a diferentes tamanhos de tela, proporcionando uma experiência consistente em todos os dispositivos.\"\n    },\n    {\n        id: 5,\n        category: 'general',\n        question: \"Como funciona o suporte técnico?\",\n        answer: \"Oferecemos suporte técnico por e-mail, chat e telefone em horário comercial. Nossa equipe de suporte está pronta para ajudar com qualquer dúvida ou problema que você possa ter. Além disso, disponibilizamos uma base de conhecimento completa com tutoriais, vídeos e artigos para ajudar você a aproveitar ao máximo o sistema.\"\n    },\n    {\n        id: 6,\n        category: 'customization',\n        question: \"O sistema pode ser personalizado para as necessidades específicas da minha clínica?\",\n        answer: \"Sim, o High Tide Systems oferece diversas opções de personalização para atender às necessidades específicas da sua clínica. Você pode configurar campos personalizados, fluxos de trabalho, relatórios e muito mais. Para necessidades mais específicas, nossa equipe de desenvolvimento pode criar soluções sob medida.\"\n    },\n    {\n        id: 7,\n        category: 'pricing',\n        question: \"Como funciona o modelo de preços?\",\n        answer: \"Oferecemos planos flexíveis baseados no número de profissionais e recursos necessários. Todos os planos incluem atualizações regulares e suporte técnico. Não há taxas de instalação ou configuração, e você pode cancelar a qualquer momento. Entre em contato conosco para obter um orçamento personalizado para sua clínica.\"\n    },\n    {\n        id: 8,\n        category: 'implementation',\n        question: \"Vocês oferecem treinamento para a equipe?\",\n        answer: \"Sim, oferecemos treinamento completo para toda a equipe da sua clínica. O treinamento pode ser realizado remotamente ou presencialmente, dependendo da sua preferência. Além disso, disponibilizamos materiais de treinamento, vídeos tutoriais e uma base de conhecimento abrangente para consulta a qualquer momento.\"\n    },\n    {\n        id: 9,\n        category: 'technical',\n        question: \"É possível integrar com outros sistemas que já utilizamos?\",\n        answer: \"Sim, o High Tide Systems oferece APIs e integrações com diversos sistemas, como sistemas contábeis, plataformas de pagamento, sistemas de prontuário eletrônico e muito mais. Nossa equipe técnica pode ajudar a configurar integrações personalizadas para atender às necessidades específicas da sua clínica.\"\n    },\n    {\n        id: 10,\n        category: 'security',\n        question: \"Como são feitos os backups dos dados?\",\n        answer: \"Realizamos backups automáticos diários de todos os dados do sistema. Os backups são armazenados em servidores redundantes em diferentes localizações geográficas para garantir a máxima segurança. Além disso, você pode solicitar backups manuais a qualquer momento ou configurar a exportação periódica dos seus dados.\"\n    },\n    {\n        id: 11,\n        category: 'pricing',\n        question: \"Existe um período de teste gratuito?\",\n        answer: \"Sim, oferecemos um período de teste gratuito de 14 dias para que você possa experimentar todas as funcionalidades do sistema antes de tomar uma decisão. Durante esse período, você terá acesso a todos os recursos e ao nosso suporte técnico para ajudar na configuração inicial e responder a quaisquer dúvidas.\"\n    },\n    {\n        id: 12,\n        category: 'customization',\n        question: \"É possível personalizar os relatórios do sistema?\",\n        answer: \"Sim, o High Tide Systems oferece um poderoso gerador de relatórios que permite criar relatórios personalizados com base em qualquer dado do sistema. Você pode definir filtros, agrupamentos, ordenações e visualizações personalizadas. Os relatórios podem ser exportados em diversos formatos, como PDF, Excel e CSV.\"\n    }\n];\nconst FAQ = ()=>{\n    _s();\n    const [openItems, setOpenItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeCategory, setActiveCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const toggleItem = (id)=>{\n        setOpenItems((prev)=>prev.includes(id) ? prev.filter((item)=>item !== id) : [\n                ...prev,\n                id\n            ]);\n    };\n    const filteredFaqs = activeCategory === 'all' ? faqs : faqs.filter((faq)=>faq.category === activeCategory);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"faq\",\n        className: \"py-20 bg-white dark:bg-gray-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 md:px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    className: \"text-center max-w-3xl mx-auto mb-12\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\",\n                            children: \"Perguntas Frequentes\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 dark:text-gray-300 mb-8\",\n                            children: \"Tire suas d\\xfavidas sobre o High Tide Systems\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap justify-center gap-2 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveCategory('all'),\n                                    className: \"px-4 py-2 rounded-full text-sm font-medium transition-colors \".concat(activeCategory === 'all' ? 'bg-primary-500 text-white' : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'),\n                                    children: \"Todas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, undefined),\n                                faqCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveCategory(category.id),\n                                        className: \"px-4 py-2 rounded-full text-sm font-medium transition-colors flex items-center \".concat(activeCategory === category.id ? 'bg-primary-500 text-white' : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: activeCategory === category.id ? 'text-white' : category.color,\n                                                children: category.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2\",\n                                                children: category.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, category.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, undefined))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: filteredFaqs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 dark:text-gray-400\",\n                            children: \"Nenhuma pergunta encontrada nesta categoria.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n                            lineNumber: 190,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n                        lineNumber: 189,\n                        columnNumber: 13\n                    }, undefined) : filteredFaqs.map((faq, index)=>{\n                        const category = faqCategories.find((c)=>c.id === faq.category);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            className: \"mb-4 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow\",\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            transition: {\n                                duration: 0.5,\n                                delay: index * 0.1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full flex items-center justify-between p-6 text-left bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                    onClick: ()=>toggleItem(faq.id),\n                                    \"aria-expanded\": openItems.includes(faq.id),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-3 \".concat(category.color),\n                                                    children: category.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                    children: faq.question\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n                                            lineNumber: 210,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 ml-4 p-1 rounded-full \".concat(openItems.includes(faq.id) ? 'bg-primary-100 dark:bg-primary-900/30' : 'bg-gray-100 dark:bg-gray-700'),\n                                            children: openItems.includes(faq.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_CreditCard_HelpCircle_Settings_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-5 w-5 \".concat(openItems.includes(faq.id) ? 'text-primary-500 dark:text-primary-400' : 'text-gray-500 dark:text-gray-400')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n                                                lineNumber: 224,\n                                                columnNumber: 25\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_CreditCard_HelpCircle_Settings_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-500 dark:text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n                                                lineNumber: 226,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n                                            lineNumber: 218,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n                                    lineNumber: 205,\n                                    columnNumber: 19\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.AnimatePresence, {\n                                    children: openItems.includes(faq.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                        initial: {\n                                            height: 0,\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            height: \"auto\",\n                                            opacity: 1\n                                        },\n                                        exit: {\n                                            height: 0,\n                                            opacity: 0\n                                        },\n                                        transition: {\n                                            duration: 0.3\n                                        },\n                                        className: \"overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 pt-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 dark:text-gray-300\",\n                                                children: faq.answer\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n                                                lineNumber: 240,\n                                                columnNumber: 27\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n                                            lineNumber: 239,\n                                            columnNumber: 25\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n                                        lineNumber: 232,\n                                        columnNumber: 23\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n                                    lineNumber: 230,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, faq.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n                            lineNumber: 197,\n                            columnNumber: 17\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    className: \"mt-12 text-center\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-300 mb-4\",\n                            children: \"N\\xe3o encontrou o que procurava?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"#contact\",\n                            onClick: (e)=>{\n                                e.preventDefault();\n                                (0,_utils_scrollUtils__WEBPACK_IMPORTED_MODULE_2__.scrollToElement)('contact', 80);\n                            },\n                            className: \"inline-flex items-center justify-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-primary-500 hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors cursor-pointer\",\n                            children: \"Entre em contato\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n            lineNumber: 140,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\FAQ.js\",\n        lineNumber: 139,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FAQ, \"tsLGZruUeEIwQUgtUBAlvQXeJQ8=\");\n_c = FAQ;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FAQ);\nvar _c;\n$RefreshReg$(_c, \"FAQ\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/FAQ.js\n"));

/***/ })

});