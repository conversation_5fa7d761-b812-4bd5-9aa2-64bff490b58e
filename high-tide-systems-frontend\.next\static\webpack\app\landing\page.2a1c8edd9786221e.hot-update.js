"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/landing/page",{

/***/ "(app-pages-browser)/./src/components/landing/Hero.js":
/*!****************************************!*\
  !*** ./src/components/landing/Hero.js ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Pause_Play_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Pause,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Pause_Play_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Pause,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Pause_Play_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Pause,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Pause_Play_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Pause,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _utils_scrollUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/scrollUtils */ \"(app-pages-browser)/./src/utils/scrollUtils.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// Imagens do slider (substitua pelos caminhos reais das suas imagens)\n// Recomendação: Use imagens com resolução de 1920x1080 ou 1600x900 pixels\n// Formato recomendado: PNG ou WebP para melhor qualidade e transparência\n// Tamanho ideal: Entre 200KB e 500KB por imagem para carregamento rápido\nconst sliderImages = [\n    {\n        id: 1,\n        src: '/landing/hero/dashboard.png',\n        alt: 'Dashboard do sistema',\n        title: 'Dashboard Intuitivo',\n        description: 'Visualize todos os dados importantes da sua clínica em um único lugar'\n    },\n    {\n        id: 2,\n        src: '/landing/hero/calendar.png',\n        alt: 'Calendário de agendamentos',\n        title: 'Agendamento Simplificado',\n        description: 'Gerencie consultas e compromissos com facilidade e eficiência'\n    },\n    {\n        id: 3,\n        src: '/landing/hero/patients.png',\n        alt: 'Gestão de pacientes',\n        title: 'Gestão de Pacientes',\n        description: 'Mantenha todos os dados dos seus pacientes organizados e acessíveis'\n    }\n];\nconst Hero = ()=>{\n    _s();\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isHovering, setIsHovering] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const sliderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const autoplayRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Função para avançar para o próximo slide\n    const nextSlide = ()=>{\n        setCurrentSlide((prev)=>prev === sliderImages.length - 1 ? 0 : prev + 1);\n    };\n    // Função para voltar para o slide anterior\n    const prevSlide = ()=>{\n        setCurrentSlide((prev)=>prev === 0 ? sliderImages.length - 1 : prev - 1);\n    };\n    // Função para ir para um slide específico\n    const goToSlide = (index)=>{\n        setCurrentSlide(index);\n    };\n    // Configurar autoplay\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Hero.useEffect\": ()=>{\n            if (isPlaying && !isHovering) {\n                autoplayRef.current = setInterval({\n                    \"Hero.useEffect\": ()=>{\n                        nextSlide();\n                    }\n                }[\"Hero.useEffect\"], 3500); // Reduzido de 5000ms para 3500ms\n            }\n            return ({\n                \"Hero.useEffect\": ()=>{\n                    if (autoplayRef.current) {\n                        clearInterval(autoplayRef.current);\n                    }\n                }\n            })[\"Hero.useEffect\"];\n        }\n    }[\"Hero.useEffect\"], [\n        isPlaying,\n        isHovering,\n        currentSlide\n    ]);\n    // Manipuladores de eventos para hover\n    const handleMouseEnter = ()=>{\n        setIsHovering(true);\n    };\n    const handleMouseLeave = ()=>{\n        setIsHovering(false);\n    };\n    // Alternar reprodução automática\n    const toggleAutoplay = ()=>{\n        setIsPlaying(!isPlaying);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative pt-32 pb-20 md:pt-40 md:pb-28 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-gray-900 dark:to-gray-800 -z-10\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-300 via-primary-500 to-primary-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-24 right-10 w-72 h-72 bg-primary-300 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-36 left-10 w-72 h-72 bg-secondary-300 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-2000\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -bottom-8 left-20 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-4000\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 md:px-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row items-center gap-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: \"flex-1 text-center lg:text-left\",\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.5\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6 leading-tight\",\n                                    children: [\n                                        \"Gerencie seu neg\\xf3cio com \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-primary-500 dark:text-primary-400\",\n                                            children: \"efici\\xeancia\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                            lineNumber: 123,\n                                            columnNumber: 40\n                                        }, undefined),\n                                        \" e \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-primary-500 dark:text-primary-400\",\n                                            children: \"simplicidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                            lineNumber: 123,\n                                            columnNumber: 117\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto lg:mx-0\",\n                                    children: \"High Tide Systems \\xe9 a solu\\xe7\\xe3o completa para seu neg\\xf3cio! Gerencie clientes e pacientes, controle agendamentos com calend\\xe1rio intuitivo, administre usu\\xe1rios e permiss\\xf5es, mantenha comunica\\xe7\\xe3o atrav\\xe9s do chat integrado e personalize o sistema conforme suas necessidades. Tudo em uma \\xfanica plataforma.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/subscription/signup\",\n                                            className: \"inline-flex items-center justify-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-primary-500 hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors\",\n                                            children: \"Come\\xe7ar agora\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#features\",\n                                            onClick: (e)=>{\n                                                e.preventDefault();\n                                                (0,_utils_scrollUtils__WEBPACK_IMPORTED_MODULE_3__.scrollToElement)('features', 80);\n                                            },\n                                            className: \"inline-flex items-center justify-center px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-base font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors cursor-pointer\",\n                                            children: \"Saiba mais\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: \"flex-1 relative\",\n                            initial: {\n                                opacity: 0,\n                                scale: 0.9\n                            },\n                            animate: {\n                                opacity: 1,\n                                scale: 1\n                            },\n                            transition: {\n                                duration: 0.5,\n                                delay: 0.2\n                            },\n                            onMouseEnter: handleMouseEnter,\n                            onMouseLeave: handleMouseLeave,\n                            ref: sliderRef,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden border border-gray-200 dark:border-gray-700 aspect-[16/10]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 bg-gray-100 dark:bg-gray-700 flex items-center px-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 bg-red-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 bg-yellow-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 bg-green-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-4 flex-1 h-5 bg-gray-200 dark:bg-gray-600 rounded-full px-2 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500 dark:text-gray-400 truncate\",\n                                                        children: \"hightide.site\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            style: {\n                                                height: \"calc(100% - 2rem)\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"overflow-hidden absolute inset-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative w-full h-full\",\n                                                        children: sliderImages.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.AnimatePresence, {\n                                                                children: currentSlide === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                                    className: \"absolute inset-0\",\n                                                                    initial: {\n                                                                        opacity: 0,\n                                                                        x: 20\n                                                                    },\n                                                                    animate: {\n                                                                        opacity: 1,\n                                                                        x: 0\n                                                                    },\n                                                                    exit: {\n                                                                        opacity: 0,\n                                                                        x: -20\n                                                                    },\n                                                                    transition: {\n                                                                        duration: 0.5\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative h-full\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 bg-gradient-to-t from-black/70 to-transparent z-10\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                                                lineNumber: 186,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                src: image.src,\n                                                                                alt: image.alt,\n                                                                                className: \"absolute inset-0 w-full h-full object-cover object-center\",\n                                                                                style: {\n                                                                                    imageRendering: 'crisp-edges',\n                                                                                    maxWidth: '100%',\n                                                                                    maxHeight: '100%'\n                                                                                },\n                                                                                loading: \"eager\",\n                                                                                onError: (e)=>{\n                                                                                    e.target.onerror = null;\n                                                                                    e.target.src = '';\n                                                                                    e.target.parentElement.innerHTML = '\\n                                    <div class=\"absolute inset-0 w-full h-full flex items-center justify-center bg-gray-200 dark:bg-gray-700\">\\n                                      <div class=\"text-center p-8\">\\n                                        <div class=\"text-primary-500 dark:text-primary-400 mx-auto mb-4\">\\n                                          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\\n                                            <rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"></rect>\\n                                            <circle cx=\"8.5\" cy=\"8.5\" r=\"1.5\"></circle>\\n                                            <polyline points=\"21 15 16 10 5 21\"></polyline>\\n                                          </svg>\\n                                        </div>\\n                                        <p class=\"text-gray-500 dark:text-gray-400\">Imagem do sistema</p>\\n                                      </div>\\n                                    </div>\\n                                  ';\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                                                lineNumber: 187,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute bottom-0 left-0 p-8 right-0 z-20\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                        className: \"text-xl font-bold text-white mb-2\",\n                                                                                        children: image.title\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                                                        lineNumber: 217,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-gray-200\",\n                                                                                        children: image.description\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                                                        lineNumber: 218,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                                                lineNumber: 216,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                                        lineNumber: 185,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                                    lineNumber: 178,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, image.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                                lineNumber: 176,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center text-white hover:bg-white/30 transition-colors z-20\",\n                                                    onClick: prevSlide,\n                                                    \"aria-label\": \"Slide anterior\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Pause_Play_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        size: 20\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center text-white hover:bg-white/30 transition-colors z-20\",\n                                                    onClick: nextSlide,\n                                                    \"aria-label\": \"Pr\\xf3ximo slide\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Pause_Play_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        size: 20\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"absolute right-4 bottom-4 w-8 h-8 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center text-white hover:bg-white/30 transition-colors z-20\",\n                                                    onClick: toggleAutoplay,\n                                                    \"aria-label\": isPlaying ? \"Pausar apresentação\" : \"Iniciar apresentação\",\n                                                    children: isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Pause_Play_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 32\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Pause_Play_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 54\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2 z-20\",\n                                                    children: sliderImages.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"w-2 h-2 rounded-full transition-all \".concat(currentSlide === index ? \"w-6 bg-white\" : \"bg-white/50 hover:bg-white/80\"),\n                                                            onClick: ()=>goToSlide(index),\n                                                            \"aria-label\": \"Ir para slide \".concat(index + 1)\n                                                        }, index, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 21\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -z-10 -right-4 -bottom-4 w-full h-full bg-gradient-to-br from-primary-200 to-primary-400 dark:from-primary-700 dark:to-primary-900 rounded-2xl\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Hero.js\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Hero, \"T5GN1rk4I4GwrSKS0Pn4jI7oKJ0=\");\n_c = Hero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Hero);\nvar _c;\n$RefreshReg$(_c, \"Hero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/Hero.js\n"));

/***/ })

});