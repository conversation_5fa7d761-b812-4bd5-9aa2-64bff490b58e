'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Users,
  Check,
  Info,
  ChevronRight
} from 'lucide-react';
import Link from 'next/link';

const Pricing = () => {
  const [isAnnual, setIsAnnual] = useState(false);
  const [userCount, setUserCount] = useState(5);

  const basePrice = 19.90; // Preço base por usuário

  // Função para calcular desconto baseado na quantidade de usuários
  const getDiscountByUserCount = (users) => {
    if (users >= 200) return 40;
    if (users >= 100) return 35;
    if (users >= 50) return 25;
    if (users >= 20) return 15;
    if (users >= 5) return 10;
    return 0;
  };

  // Função para calcular preços
  const calculatePrice = (users) => {
    const discount = getDiscountByUserCount(users);
    const totalWithoutDiscount = users * basePrice;
    const discountAmount = totalWithoutDiscount * (discount / 100);
    const finalPrice = totalWithoutDiscount - discountAmount;

    // Desconto adicional de 10% para pagamento anual à vista
    const annualDiscount = 0.10;
    const yearlyPriceWithDiscount = (finalPrice * 12) * (1 - annualDiscount);

    return {
      totalWithoutDiscount,
      discountAmount,
      finalPrice,
      discount,
      monthlyPrice: finalPrice,
      yearlyPrice: finalPrice * 12,
      yearlyPriceWithDiscount,
      annualDiscount: annualDiscount * 100
    };
  };

  const pricing = calculatePrice(userCount);

  // Opções de usuários predefinidas
  const userOptions = [1, 5, 10, 20, 50, 100, 200];



  return (
    <section id="pricing" className="py-16 bg-gray-50 dark:bg-gray-800">
      <div className="container mx-auto px-4 md:px-6">
        <motion.div
          className="text-center max-w-3xl mx-auto mb-10"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Preços flexíveis baseados em usuários
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-6">
            Pague apenas pelos usuários que você precisa. Escolha a quantidade ideal para o seu negócio.
          </p>

          {/* Toggle Anual/Mensal */}
          <div className="flex items-center justify-center">
            <span className={`mr-3 ${!isAnnual ? 'text-gray-900 dark:text-white font-medium' : 'text-gray-600 dark:text-gray-300'}`}>
              Mensal
            </span>
            <button
              onClick={() => setIsAnnual(!isAnnual)}
              className={`relative inline-flex h-6 w-14 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 ${
                isAnnual ? 'bg-orange-600' : 'bg-gray-300 dark:bg-gray-600'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition ${
                  isAnnual ? 'translate-x-9' : 'translate-x-1'
                }`}
              />
            </button>
            <span className={`ml-3 ${isAnnual ? 'text-gray-900 dark:text-white font-medium' : 'text-gray-600 dark:text-gray-300'}`}>
              Anual
            </span>
          </div>
        </motion.div>

        {/* Seletor de usuários */}
        <motion.div
          className="max-w-4xl mx-auto mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="bg-white dark:bg-gray-900 rounded-xl shadow-lg p-8 border border-gray-200 dark:border-gray-700">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                Quantos usuários você precisa?
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Selecione a quantidade de usuários para ver o preço personalizado
              </p>
            </div>

            {/* Seletor de usuários */}
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-3 mb-8">
              {userOptions.map((option) => (
                <button
                  key={option}
                  onClick={() => setUserCount(option)}
                  className={`p-4 rounded-lg border-2 transition-all text-center ${
                    userCount === option
                      ? 'border-orange-500 bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-400'
                      : 'border-gray-200 dark:border-gray-700 hover:border-orange-300 dark:hover:border-orange-600'
                  }`}
                >
                  <div className="text-lg font-bold text-gray-900 dark:text-white">
                    {option}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {option === 1 ? 'usuário' : 'usuários'}
                  </div>
                </button>
              ))}
            </div>

            {/* Input customizado */}
            <div className="text-center mb-8">
              <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                Ou digite uma quantidade personalizada:
              </p>
              <div className="flex justify-center">
                <div className="relative">
                  <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    type="number"
                    min="1"
                    max="1000"
                    value={userCount}
                    onChange={(e) => setUserCount(Math.max(1, parseInt(e.target.value) || 1))}
                    className="pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900 dark:text-white bg-white dark:bg-gray-700 w-32 text-center"
                    placeholder="1"
                  />
                </div>
              </div>
            </div>

            {/* Resumo do plano */}
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Informações do Plano */}
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Usuários:</span>
                    <span className="font-medium text-gray-900 dark:text-white">{userCount}</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Preço por usuário:</span>
                    <div className="text-right">
                      {pricing.discount > 0 ? (
                        <>
                          <span className="text-sm text-gray-500 dark:text-gray-400 line-through">
                            R$ {basePrice.toFixed(2).replace('.', ',')}
                          </span>
                          <span className="ml-2 font-medium text-gray-900 dark:text-white">
                            R$ {(basePrice * (1 - pricing.discount / 100)).toFixed(2).replace('.', ',')}
                          </span>
                        </>
                      ) : (
                        <span className="font-medium text-gray-900 dark:text-white">
                          R$ {basePrice.toFixed(2).replace('.', ',')}
                        </span>
                      )}
                    </div>
                  </div>

                  {pricing.discount > 0 && (
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Desconto por quantidade:</span>
                      <span className="font-medium text-green-600 dark:text-green-400">-{pricing.discount}%</span>
                    </div>
                  )}

                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Valor mensal sem desconto:</span>
                    <span className="text-sm text-gray-500 dark:text-gray-400 line-through">
                      R$ {pricing.totalWithoutDiscount.toFixed(2).replace('.', ',')}
                    </span>
                  </div>

                  {isAnnual && (
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Desconto anual à vista:</span>
                      <span className="font-medium text-green-600 dark:text-green-400">-{pricing.annualDiscount}%</span>
                    </div>
                  )}
                </div>

                {/* Preço Final */}
                <div className="text-center md:text-right">
                  <div className="mb-2">
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      Valor {isAnnual ? 'anual à vista' : 'mensal'}:
                    </div>

                    {isAnnual ? (
                      <>
                        <div className="text-lg text-gray-500 dark:text-gray-400 line-through">
                          R$ {pricing.yearlyPrice.toFixed(2).replace('.', ',')}
                        </div>
                        <div className="text-3xl font-bold text-orange-600 dark:text-orange-400">
                          R$ {pricing.yearlyPriceWithDiscount.toFixed(2).replace('.', ',')}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          R$ {(pricing.yearlyPriceWithDiscount / 12).toFixed(2).replace('.', ',')} por mês
                        </div>
                      </>
                    ) : (
                      <div className="text-3xl font-bold text-orange-600 dark:text-orange-400">
                        R$ {pricing.monthlyPrice.toFixed(2).replace('.', ',')}
                      </div>
                    )}
                  </div>

                  {/* Badges de economia */}
                  <div className="space-y-2">
                    {pricing.discount > 0 && (
                      <div className="text-center md:text-right">
                        <span className="bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 px-3 py-1 rounded-full text-sm font-medium">
                          Economia de R$ {pricing.discountAmount.toFixed(2).replace('.', ',')} por mês
                        </span>
                      </div>
                    )}

                    {isAnnual && (
                      <div className="text-center md:text-right">
                        <span className="bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400 px-3 py-1 rounded-full text-sm font-medium">
                          Economia adicional de R$ {((pricing.yearlyPrice - pricing.yearlyPriceWithDiscount)).toFixed(2).replace('.', ',')} no ano
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="mt-6 text-center">
                <Link
                  href="/subscription/signup"
                  className="inline-flex items-center justify-center px-8 py-3 border border-transparent rounded-lg shadow-sm text-base font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors"
                >
                  Começar agora
                  <ChevronRight className="ml-2 h-5 w-5" />
                </Link>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Informações adicionais */}
        <div className="mt-12 text-center max-w-5xl mx-auto">
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Todos os planos incluem:
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <div className="bg-white dark:bg-gray-900 p-4 rounded-lg shadow flex-1 min-w-[200px] max-w-[300px]">
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">Suporte técnico</h4>
              <p className="text-gray-500 dark:text-gray-400 text-sm">Suporte por e-mail e chat em horário comercial</p>
            </div>
            <div className="bg-white dark:bg-gray-900 p-4 rounded-lg shadow flex-1 min-w-[200px] max-w-[300px]">
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">Atualizações</h4>
              <p className="text-gray-500 dark:text-gray-400 text-sm">Acesso a todas as atualizações e novos recursos</p>
            </div>
            <div className="bg-white dark:bg-gray-900 p-4 rounded-lg shadow flex-1 min-w-[200px] max-w-[300px]">
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">Segurança</h4>
              <p className="text-gray-500 dark:text-gray-400 text-sm">Backup diário e proteção de dados conforme LGPD</p>
            </div>
          </div>
          <p className="mt-6 text-sm text-gray-500 dark:text-gray-400">
            Descontos automáticos para equipes maiores. Quanto mais usuários, maior o desconto!
          </p>
        </div>
      </div>
    </section>
  );
};



export default Pricing;
