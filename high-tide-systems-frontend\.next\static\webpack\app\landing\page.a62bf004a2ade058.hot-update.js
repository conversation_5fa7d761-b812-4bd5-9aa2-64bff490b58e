"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/landing/page",{

/***/ "(app-pages-browser)/./src/components/landing/Modules.js":
/*!*******************************************!*\
  !*** ./src/components/landing/Modules.js ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_Calendar_MessageSquare_Settings_ShieldCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,Calendar,MessageSquare,Settings,ShieldCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_Calendar_MessageSquare_Settings_ShieldCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,Calendar,MessageSquare,Settings,ShieldCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_Calendar_MessageSquare_Settings_ShieldCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,Calendar,MessageSquare,Settings,ShieldCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_Calendar_MessageSquare_Settings_ShieldCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,Calendar,MessageSquare,Settings,ShieldCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column-increasing.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_Calendar_MessageSquare_Settings_ShieldCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,Calendar,MessageSquare,Settings,ShieldCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_Calendar_MessageSquare_Settings_ShieldCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,Calendar,MessageSquare,Settings,ShieldCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst modules = [\n    {\n        id: 'scheduler',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_Calendar_MessageSquare_Settings_ShieldCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            size: 24\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n            lineNumber: 16,\n            columnNumber: 11\n        }, undefined),\n        title: 'Agendamento',\n        description: 'Gerencie consultas, compromissos e disponibilidade dos profissionais com um calendário intuitivo e completo.',\n        features: [\n            'Visualização diária, semanal e mensal',\n            'Agendamentos recorrentes',\n            'Detecção de conflitos',\n            'Horários de trabalho personalizáveis',\n            'Notificações automáticas'\n        ],\n        color: 'scheduler'\n    },\n    {\n        id: 'people',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_Calendar_MessageSquare_Settings_ShieldCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            size: 24\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n            lineNumber: 30,\n            columnNumber: 11\n        }, undefined),\n        title: 'Pessoas',\n        description: 'Cadastro completo de pacientes, profissionais e colaboradores com todas as informações necessárias.',\n        features: [\n            'Cadastro de pacientes e contatos',\n            'Histórico de atendimentos',\n            'Gestão de convênios',\n            'Documentos digitalizados',\n            'Relacionamentos familiares'\n        ],\n        color: 'people'\n    },\n    {\n        id: 'admin',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_Calendar_MessageSquare_Settings_ShieldCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            size: 24\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n            lineNumber: 45,\n            columnNumber: 11\n        }, undefined),\n        title: 'Administração',\n        description: 'Gerencie usuários, permissões, configurações e mantenha o controle total do sistema.',\n        features: [\n            'Gestão de usuários e permissões',\n            'Configurações do sistema',\n            'Logs de atividades',\n            'Backup de dados',\n            'Segurança e privacidade'\n        ],\n        color: 'admin'\n    },\n    {\n        id: 'reports',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_Calendar_MessageSquare_Settings_ShieldCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            size: 24\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n            lineNumber: 59,\n            columnNumber: 11\n        }, undefined),\n        title: 'Relatórios',\n        description: 'Dashboards e relatórios detalhados para análise de desempenho e tomada de decisões.',\n        features: [\n            'Dashboard interativo',\n            'Relatórios personalizáveis',\n            'Análise de ocupação',\n            'Indicadores de desempenho',\n            'Exportação em diversos formatos'\n        ],\n        color: 'reports'\n    },\n    {\n        id: 'chat',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_Calendar_MessageSquare_Settings_ShieldCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            size: 24\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n            lineNumber: 73,\n            columnNumber: 11\n        }, undefined),\n        title: 'Chat',\n        description: 'Sistema de comunicação integrado para facilitar a comunicação entre equipe e pacientes.',\n        features: [\n            'Chat em tempo real',\n            'Comunicação interna da equipe',\n            'Histórico de conversas',\n            'Notificações de mensagens',\n            'Interface intuitiva e responsiva'\n        ],\n        color: 'chat'\n    },\n    {\n        id: 'customization',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_Calendar_MessageSquare_Settings_ShieldCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            size: 24\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n            lineNumber: 87,\n            columnNumber: 11\n        }, undefined),\n        title: 'Personalização',\n        description: 'Adapte o sistema às necessidades específicas do seu negócio com opções de personalização.',\n        features: [\n            'Configurações personalizáveis',\n            'Campos customizados',\n            'Fluxos de trabalho adaptáveis',\n            'Interface configurável',\n            'Relatórios sob medida'\n        ],\n        color: 'customization'\n    }\n];\nconst Modules = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"modules\",\n        className: \"py-20 bg-gray-50 dark:bg-gray-800\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 md:px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    className: \"text-center max-w-3xl mx-auto mb-16\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\",\n                            children: \"M\\xf3dulos integrados\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 dark:text-gray-300\",\n                            children: \"Cada m\\xf3dulo foi desenvolvido para atender necessidades espec\\xedficas, mas todos trabalham em perfeita harmonia.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-16\",\n                    children: modules.map((module, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            className: \"flex flex-col \".concat(index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse', \" gap-8 items-center\"),\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            transition: {\n                                duration: 0.5\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white dark:bg-gray-900 rounded-xl shadow-lg overflow-hidden border border-gray-100 dark:border-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6 \".concat(module.id === 'scheduler' ? 'bg-purple-50 dark:bg-purple-900/20' : module.id === 'people' ? 'bg-amber-50 dark:bg-amber-900/20' : module.id === 'admin' ? 'bg-gray-50 dark:bg-gray-900/20' : module.id === 'reports' ? 'bg-blue-50 dark:bg-blue-900/20' : module.id === 'chat' ? 'bg-green-50 dark:bg-green-900/20' : module.id === 'customization' ? 'bg-orange-50 dark:bg-orange-900/20' : 'bg-gray-50 dark:bg-gray-900/20'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 rounded-lg \".concat(module.id === 'scheduler' ? 'bg-purple-100 dark:bg-purple-800/30' : module.id === 'people' ? 'bg-amber-100 dark:bg-amber-800/30' : module.id === 'admin' ? 'bg-gray-100 dark:bg-gray-800/30' : module.id === 'reports' ? 'bg-blue-100 dark:bg-blue-800/30' : module.id === 'chat' ? 'bg-green-100 dark:bg-green-800/30' : module.id === 'customization' ? 'bg-orange-100 dark:bg-orange-800/30' : 'bg-gray-100 dark:bg-gray-800/30', \" flex items-center justify-center mr-4\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: module.id === 'scheduler' ? 'text-purple-600 dark:text-purple-400' : module.id === 'people' ? 'text-amber-600 dark:text-amber-400' : module.id === 'admin' ? 'text-red-600 dark:text-red-400' : module.id === 'reports' ? 'text-blue-600 dark:text-blue-400' : module.id === 'chat' ? 'text-green-600 dark:text-green-400' : module.id === 'customization' ? 'text-orange-600 dark:text-orange-400' : 'text-gray-600 dark:text-gray-400',\n                                                                children: module.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                                lineNumber: 152,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl font-bold \".concat(module.id === 'scheduler' ? 'text-purple-800 dark:text-purple-300' : module.id === 'people' ? 'text-amber-800 dark:text-amber-300' : module.id === 'admin' ? 'text-red-800 dark:text-red-300' : module.id === 'reports' ? 'text-blue-800 dark:text-blue-300' : module.id === 'chat' ? 'text-green-800 dark:text-green-300' : module.id === 'customization' ? 'text-orange-800 dark:text-orange-300' : 'text-gray-800 dark:text-gray-300'),\n                                                            children: module.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                lineNumber: 133,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 dark:text-gray-300 mb-6\",\n                                                        children: module.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-3\",\n                                                        children: module.features.map((feature, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-1 mr-3 w-5 h-5 rounded-full flex items-center justify-center \".concat(module.id === 'scheduler' ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400' : module.id === 'people' ? 'bg-amber-100 dark:bg-amber-900/30 text-amber-600 dark:text-amber-400' : module.id === 'admin' ? 'bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400' : module.id === 'reports' ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400' : module.id === 'chat' ? 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400' : module.id === 'customization' ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400' : 'bg-gray-100 dark:bg-gray-900/30 text-gray-600 dark:text-gray-400'),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"h-3 w-3\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            fill: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                                                lineNumber: 194,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                                            lineNumber: 193,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                                        lineNumber: 184,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-700 dark:text-gray-300\",\n                                                                        children: feature\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                                        lineNumber: 197,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, i, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                                lineNumber: 183,\n                                                                columnNumber: 25\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                lineNumber: 177,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                        lineNumber: 132,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden border border-gray-200 dark:border-gray-700 p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"aspect-video bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mx-auto mb-4 \".concat(module.id === 'scheduler' ? 'text-purple-600 dark:text-purple-400' : module.id === 'people' ? 'text-amber-600 dark:text-amber-400' : module.id === 'admin' ? 'text-red-600 dark:text-red-400' : module.id === 'reports' ? 'text-blue-600 dark:text-blue-400' : module.id === 'chat' ? 'text-green-600 dark:text-green-400' : module.id === 'customization' ? 'text-orange-600 dark:text-orange-400' : 'text-gray-600 dark:text-gray-400'),\n                                                        children: module.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-xl font-semibold text-gray-900 dark:text-white mb-2\",\n                                                        children: [\n                                                            \"M\\xf3dulo de \",\n                                                            module.title\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 dark:text-gray-400\",\n                                                        children: \"Interface intuitiva e funcional para gerenciar todas as atividades relacionadas.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                                lineNumber: 209,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                            lineNumber: 208,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                        lineNumber: 207,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, module.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Modules.js\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Modules;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Modules);\nvar _c;\n$RefreshReg$(_c, \"Modules\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/Modules.js\n"));

/***/ })

});