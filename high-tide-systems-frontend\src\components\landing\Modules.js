'use client';

import { motion } from 'framer-motion';
import {
  ShieldCheck,
  Calendar,
  Users,
  BarChart4,
  MessageSquare,
  Settings
} from 'lucide-react';

const modules = [
  {
    id: 'scheduler',
    icon: <Calendar size={24} />,
    title: 'Agendamento',
    description: 'Gere<PERSON><PERSON> consultas, compromissos e disponibilidade dos profissionais com um calendário intuitivo e completo.',
    features: [
      'Visualização diária, semanal e mensal',
      'Agendamentos recorrentes',
      'Detecção de conflitos',
      'Horários de trabalho personalizáveis',
      'Notificações automáticas'
    ],
    color: 'scheduler'
  },
  {
    id: 'people',
    icon: <Users size={24} />,
    title: 'P<PERSON>oa<PERSON>',
    description: 'Cadastro completo de pacientes, profissionais e colaboradores com todas as informações necessárias.',
    features: [
      'Cadastro de pacientes e contatos',
      'Histórico de atendimentos',
      'Gestão de convênios',
      'Documentos digitalizados',
      'Relacionamentos familiares'
    ],
    color: 'people'
  },

  {
    id: 'admin',
    icon: <ShieldCheck size={24} />,
    title: 'Administração',
    description: 'Gerencie usuários, permissões, configurações e mantenha o controle total do sistema.',
    features: [
      'Gestão de usuários e permissões',
      'Configurações do sistema',
      'Logs de atividades',
      'Backup de dados',
      'Segurança e privacidade'
    ],
    color: 'admin'
  },
  {
    id: 'reports',
    icon: <BarChart4 size={24} />,
    title: 'Relatórios',
    description: 'Dashboards e relatórios detalhados para análise de desempenho e tomada de decisões.',
    features: [
      'Dashboard interativo',
      'Relatórios personalizáveis',
      'Análise de ocupação',
      'Indicadores de desempenho',
      'Exportação em diversos formatos'
    ],
    color: 'reports'
  },
  {
    id: 'chat',
    icon: <MessageSquare size={24} />,
    title: 'Chat',
    description: 'Sistema de comunicação integrado para facilitar a comunicação entre equipe e pacientes.',
    features: [
      'Chat em tempo real',
      'Comunicação interna da equipe',
      'Histórico de conversas',
      'Notificações de mensagens',
      'Interface intuitiva e responsiva'
    ],
    color: 'chat'
  },
  {
    id: 'customization',
    icon: <Settings size={24} />,
    title: 'Personalização',
    description: 'Adapte o sistema às necessidades específicas do seu negócio com opções de personalização.',
    features: [
      'Configurações personalizáveis',
      'Campos customizados',
      'Fluxos de trabalho adaptáveis',
      'Interface configurável',
      'Relatórios sob medida'
    ],
    color: 'customization'
  }
];

const Modules = () => {
  return (
    <section id="modules" className="py-20 bg-gray-50 dark:bg-gray-800">
      <div className="container mx-auto px-4 md:px-6">
        <motion.div 
          className="text-center max-w-3xl mx-auto mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Módulos integrados
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300">
            Cada módulo foi desenvolvido para atender necessidades específicas, mas todos trabalham em perfeita harmonia.
          </p>
        </motion.div>

        <div className="space-y-16">
          {modules.map((module, index) => (
            <motion.div 
              key={module.id}
              className={`flex flex-col ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'} gap-8 items-center`}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              {/* Module Card */}
              <div className="flex-1">
                <div className={`bg-white dark:bg-gray-900 rounded-xl shadow-lg overflow-hidden border border-gray-100 dark:border-gray-700`}>
                  <div className={`p-6 ${
                    module.id === 'scheduler' ? 'bg-purple-50 dark:bg-purple-900/20' :
                    module.id === 'people' ? 'bg-amber-50 dark:bg-amber-900/20' :
                    module.id === 'admin' ? 'bg-red-50 dark:bg-red-900/20' :
                    module.id === 'reports' ? 'bg-blue-50 dark:bg-blue-900/20' :
                    module.id === 'chat' ? 'bg-green-50 dark:bg-green-900/20' :
                    module.id === 'customization' ? 'bg-orange-50 dark:bg-orange-900/20' :
                    'bg-gray-50 dark:bg-gray-900/20'
                  }`}>
                    <div className="flex items-center">
                      <div className={`w-12 h-12 rounded-lg ${
                        module.id === 'scheduler' ? 'bg-purple-100 dark:bg-purple-800/30' :
                        module.id === 'people' ? 'bg-amber-100 dark:bg-amber-800/30' :
                        module.id === 'admin' ? 'bg-red-100 dark:bg-red-800/30' :
                        module.id === 'reports' ? 'bg-blue-100 dark:bg-blue-800/30' :
                        module.id === 'chat' ? 'bg-green-100 dark:bg-green-800/30' :
                        module.id === 'customization' ? 'bg-orange-100 dark:bg-orange-800/30' :
                        'bg-gray-100 dark:bg-gray-800/30'
                      } flex items-center justify-center mr-4`}>
                        <div className={
                          module.id === 'scheduler' ? 'text-purple-600 dark:text-purple-400' :
                          module.id === 'people' ? 'text-amber-600 dark:text-amber-400' :
                          module.id === 'admin' ? 'text-red-600 dark:text-red-400' :
                          module.id === 'reports' ? 'text-blue-600 dark:text-blue-400' :
                          module.id === 'chat' ? 'text-green-600 dark:text-green-400' :
                          module.id === 'customization' ? 'text-orange-600 dark:text-orange-400' :
                          'text-gray-600 dark:text-gray-400'
                        }>
                          {module.icon}
                        </div>
                      </div>
                      <h3 className={`text-2xl font-bold ${
                        module.id === 'scheduler' ? 'text-purple-800 dark:text-purple-300' :
                        module.id === 'people' ? 'text-amber-800 dark:text-amber-300' :
                        module.id === 'admin' ? 'text-red-800 dark:text-red-300' :
                        module.id === 'reports' ? 'text-blue-800 dark:text-blue-300' :
                        module.id === 'chat' ? 'text-green-800 dark:text-green-300' :
                        module.id === 'customization' ? 'text-orange-800 dark:text-orange-300' :
                        'text-gray-800 dark:text-gray-300'
                      }`}>
                        {module.title}
                      </h3>
                    </div>
                  </div>
                  <div className="p-6">
                    <p className="text-gray-600 dark:text-gray-300 mb-6">
                      {module.description}
                    </p>
                    <ul className="space-y-3">
                      {module.features.map((feature, i) => (
                        <li key={i} className="flex items-start">
                          <div className={`mt-1 mr-3 w-5 h-5 rounded-full flex items-center justify-center ${
                            module.id === 'scheduler' ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400' :
                            module.id === 'people' ? 'bg-amber-100 dark:bg-amber-900/30 text-amber-600 dark:text-amber-400' :
                            module.id === 'admin' ? 'bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400' :
                            module.id === 'reports' ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400' :
                            module.id === 'chat' ? 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400' :
                            module.id === 'customization' ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400' :
                            'bg-gray-100 dark:bg-gray-900/30 text-gray-600 dark:text-gray-400'
                          }`}>
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </div>
                          <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>

              {/* Module Screenshot/Illustration */}
              <div className="flex-1">
                <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden border border-gray-200 dark:border-gray-700 p-4">
                  <div className="aspect-video bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                    <div className="text-center p-8">
                      <div className={`mx-auto mb-4 ${
                        module.id === 'scheduler' ? 'text-purple-600 dark:text-purple-400' :
                        module.id === 'people' ? 'text-amber-600 dark:text-amber-400' :
                        module.id === 'admin' ? 'text-red-600 dark:text-red-400' :
                        module.id === 'reports' ? 'text-blue-600 dark:text-blue-400' :
                        module.id === 'chat' ? 'text-green-600 dark:text-green-400' :
                        module.id === 'customization' ? 'text-orange-600 dark:text-orange-400' :
                        'text-gray-600 dark:text-gray-400'
                      }`}>
                        {module.icon}
                      </div>
                      <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                        Módulo de {module.title}
                      </h4>
                      <p className="text-gray-500 dark:text-gray-400">
                        Interface intuitiva e funcional para gerenciar todas as atividades relacionadas.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Modules;
