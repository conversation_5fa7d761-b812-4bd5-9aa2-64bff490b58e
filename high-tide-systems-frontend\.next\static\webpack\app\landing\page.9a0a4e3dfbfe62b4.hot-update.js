"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/landing/page",{

/***/ "(app-pages-browser)/./src/components/landing/Pricing.js":
/*!*******************************************!*\
  !*** ./src/components/landing/Pricing.js ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Info_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Info,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Info_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Info,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst Pricing = ()=>{\n    _s();\n    const [isAnnual, setIsAnnual] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userCount, setUserCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(5);\n    const basePrice = 19.90; // Preço base por usuário\n    // Função para calcular desconto baseado na quantidade de usuários\n    const getDiscountByUserCount = (users)=>{\n        if (users >= 200) return 40;\n        if (users >= 100) return 35;\n        if (users >= 50) return 25;\n        if (users >= 20) return 15;\n        if (users >= 5) return 10;\n        return 0;\n    };\n    // Função para calcular preços\n    const calculatePrice = (users)=>{\n        const discount = getDiscountByUserCount(users);\n        const totalWithoutDiscount = users * basePrice;\n        const discountAmount = totalWithoutDiscount * (discount / 100);\n        const finalPrice = totalWithoutDiscount - discountAmount;\n        return {\n            totalWithoutDiscount,\n            discountAmount,\n            finalPrice,\n            discount,\n            monthlyPrice: finalPrice,\n            yearlyPrice: finalPrice * 12\n        };\n    };\n    const pricing = calculatePrice(userCount);\n    // Opções de usuários predefinidas\n    const userOptions = [\n        1,\n        5,\n        10,\n        20,\n        50,\n        100,\n        200\n    ];\n    // Função para alternar a seleção de um módulo\n    const toggleModule = (moduleId)=>{\n        if (selectedModules.includes(moduleId)) {\n            setSelectedModules(selectedModules.filter((id)=>id !== moduleId));\n        } else {\n            setSelectedModules([\n                ...selectedModules,\n                moduleId\n            ]);\n        }\n    };\n    // Calcular o preço total com base nos módulos selecionados\n    const calculateTotal = ()=>{\n        let total = 0;\n        selectedModules.forEach((moduleId)=>{\n            const module = modules.find((m)=>m.id === moduleId);\n            if (module) {\n                total += isAnnual ? module.annualPrice : module.monthlyPrice;\n            }\n        });\n        // Aplicar desconto com base no número de módulos selecionados\n        if (selectedModules.length >= 3) {\n            total = total * 0.9; // 10% de desconto para 3 ou mais módulos\n        } else if (selectedModules.length === 2) {\n            total = total * 0.95; // 5% de desconto para 2 módulos\n        }\n        return total;\n    };\n    // Calcular o valor mensal (mesmo para plano anual)\n    const calculateMonthlyValue = ()=>{\n        const total = calculateTotal();\n        return isAnnual ? (total / 12).toFixed(0) : total;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"pricing\",\n        className: \"py-16 bg-gray-50 dark:bg-gray-800\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 md:px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    className: \"text-center max-w-3xl mx-auto mb-10\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\",\n                            children: \"Pre\\xe7os flex\\xedveis baseados em m\\xf3dulos\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 dark:text-gray-300 mb-6\",\n                            children: \"Pague apenas pelos m\\xf3dulos que voc\\xea precisa. Escolha os m\\xf3dulos ideais para o seu neg\\xf3cio.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center space-x-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab('modules'),\n                                    className: \"px-4 py-2 rounded-lg font-medium transition-colors \".concat(activeTab === 'modules' ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'),\n                                    children: \"M\\xf3dulos\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab('comparison'),\n                                    className: \"px-4 py-2 rounded-lg font-medium transition-colors \".concat(activeTab === 'comparison' ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'),\n                                    children: \"Comparativo\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"mr-3 \".concat(!isAnnual ? 'text-gray-900 dark:text-white font-medium' : 'text-gray-600 dark:text-gray-300'),\n                                    children: \"Mensal\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsAnnual(!isAnnual),\n                                    className: \"relative inline-flex h-6 w-14 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 \".concat(isAnnual ? 'bg-blue-600' : 'bg-gray-300 dark:bg-gray-600'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-block h-4 w-4 transform rounded-full bg-white transition \".concat(isAnnual ? 'translate-x-9' : 'translate-x-1')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-3 \".concat(isAnnual ? 'text-gray-900 dark:text-white font-medium' : 'text-gray-600 dark:text-gray-300'),\n                                    children: [\n                                        \"Anual \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-500 text-sm font-medium\",\n                                            children: \"(2 meses gr\\xe1tis)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 145,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, undefined),\n                activeTab === 'modules' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModulesView, {\n                    modules: modules,\n                    selectedModules: selectedModules,\n                    toggleModule: toggleModule,\n                    isAnnual: isAnnual,\n                    calculateMonthlyValue: calculateMonthlyValue,\n                    calculateTotal: calculateTotal\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                    lineNumber: 152,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ComparisonView, {\n                    modules: modules,\n                    isAnnual: isAnnual,\n                    toggleModule: toggleModule,\n                    setActiveTab: setActiveTab\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                    lineNumber: 161,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12 text-center max-w-5xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-300 mb-4\",\n                            children: \"Todos os planos incluem:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap justify-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white dark:bg-gray-900 p-4 rounded-lg shadow flex-1 min-w-[200px] max-w-[300px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-gray-900 dark:text-white mb-2\",\n                                            children: \"Suporte t\\xe9cnico\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 dark:text-gray-400 text-sm\",\n                                            children: \"Suporte por e-mail e chat em hor\\xe1rio comercial\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white dark:bg-gray-900 p-4 rounded-lg shadow flex-1 min-w-[200px] max-w-[300px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-gray-900 dark:text-white mb-2\",\n                                            children: \"Atualiza\\xe7\\xf5es\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 dark:text-gray-400 text-sm\",\n                                            children: \"Acesso a todas as atualiza\\xe7\\xf5es e novos recursos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white dark:bg-gray-900 p-4 rounded-lg shadow flex-1 min-w-[200px] max-w-[300px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-gray-900 dark:text-white mb-2\",\n                                            children: \"Seguran\\xe7a\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 dark:text-gray-400 text-sm\",\n                                            children: \"Backup di\\xe1rio e prote\\xe7\\xe3o de dados conforme LGPD\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-6 text-sm text-gray-500 dark:text-gray-400\",\n                            children: \"Pre\\xe7os para at\\xe9 5 usu\\xe1rios. Para equipes maiores ou necessidades espec\\xedficas, entre em contato para um or\\xe7amento personalizado.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n            lineNumber: 88,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Pricing, \"VTbvw8HivIaqBCZeV9Lfr85BgVo=\");\n_c = Pricing;\n// Componente para a visualização de módulos\nconst ModulesView = (param)=>{\n    let { modules: modules1, selectedModules: selectedModules1, toggleModule, isAnnual, calculateMonthlyValue, calculateTotal } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap justify-center gap-4 mb-8\",\n                children: modules1.map((module, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.5,\n                            delay: index * 0.1\n                        },\n                        className: \"bg-white dark:bg-gray-900 rounded-xl shadow-lg overflow-hidden border-2 transition-all flex-1 min-w-[250px] max-w-[350px] \".concat(selectedModules1.includes(module.id) ? (module.id === 'scheduler' ? 'border-purple-500 dark:border-purple-400' : module.id === 'people' ? 'border-amber-500 dark:border-amber-400' : module.id === 'financial' ? 'border-emerald-500 dark:border-emerald-400' : module.id === 'admin' ? 'border-red-500 dark:border-red-400' : 'border-blue-500 dark:border-blue-400') + ' transform scale-[1.02]' : 'border-transparent'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-5 flex flex-col h-full justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 rounded-lg flex items-center justify-center \".concat(module.colorClass),\n                                            children: module.icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 222,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-bold text-gray-900 dark:text-white\",\n                                                    children: module.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 dark:text-gray-400 text-sm\",\n                                                    children: [\n                                                        \"R$ \",\n                                                        isAnnual ? (module.annualPrice / 12).toFixed(0) : module.monthlyPrice,\n                                                        \"/m\\xeas\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 225,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 221,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300 mb-4 text-sm\",\n                                    children: module.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 235,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Info_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-500 mr-2 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600 dark:text-gray-300 text-sm\",\n                                                    children: module.features[0]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 240,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Info_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-500 mr-2 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600 dark:text-gray-300 text-sm\",\n                                                    children: module.features[1]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 244,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 239,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>toggleModule(module.id),\n                                    className: \"w-full py-2 px-3 rounded-lg font-medium transition-colors text-sm \".concat(selectedModules1.includes(module.id) ? module.id === 'scheduler' ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 border border-purple-300 dark:border-purple-700' : module.id === 'people' ? 'bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-400 border border-amber-300 dark:border-amber-700' : module.id === 'financial' ? 'bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-400 border border-emerald-300 dark:border-emerald-700' : module.id === 'admin' ? 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 border border-red-300 dark:border-red-700' : 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 border border-blue-300 dark:border-blue-700' : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700 hover:bg-gray-200 dark:hover:bg-gray-700'),\n                                    children: selectedModules1.includes(module.id) ? 'Selecionado' : 'Selecionar'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 250,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                            lineNumber: 220,\n                            columnNumber: 13\n                        }, undefined)\n                    }, module.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                        lineNumber: 204,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                whileInView: {\n                    opacity: 1,\n                    y: 0\n                },\n                viewport: {\n                    once: true\n                },\n                transition: {\n                    duration: 0.5\n                },\n                className: \"max-w-5xl mx-auto bg-white dark:bg-gray-900 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full lg:w-1/2 mb-4 lg:mb-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-gray-900 dark:text-white mb-2\",\n                                    children: \"Resumo do seu plano\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, undefined),\n                                selectedModules1.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500 dark:text-gray-400 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Info_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 285,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Selecione pelo menos um m\\xf3dulo para ver o pre\\xe7o total\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 284,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: selectedModules1.map((moduleId)=>{\n                                        const module = modules1.find((m)=>m.id === moduleId);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center bg-gray-100 dark:bg-gray-800 rounded-full py-1 px-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-5 h-5 rounded-full flex items-center justify-center mr-1 \".concat(module.colorClass),\n                                                    children: module.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-700 dark:text-gray-300 text-sm\",\n                                                    children: module.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, moduleId, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 293,\n                                            columnNumber: 21\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full lg:w-1/2 text-center lg:text-right\",\n                            children: selectedModules1.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500 dark:text-gray-400 text-sm\",\n                                                children: \"Valor mensal:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                lineNumber: 309,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n                                                children: [\n                                                    \"R$ \",\n                                                    calculateMonthlyValue(),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400 font-normal\",\n                                                        children: \"/m\\xeas\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                lineNumber: 310,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                        lineNumber: 308,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    isAnnual && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-500 dark:text-gray-400 text-sm mb-3\",\n                                        children: [\n                                            \"Valor total anual: R$ \",\n                                            calculateTotal().toFixed(0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-500 ml-1\",\n                                                children: [\n                                                    \"(economia de R$ \",\n                                                    (calculateTotal() * 0.2).toFixed(0),\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                lineNumber: 319,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                        lineNumber: 317,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    selectedModules1.length >= 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-green-500 text-sm mb-3\",\n                                        children: [\n                                            \"Desconto aplicado: \",\n                                            selectedModules1.length >= 3 ? '10%' : '5%'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                        lineNumber: 324,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-5 rounded-lg transition-colors\",\n                                        children: \"Come\\xe7ar agora\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                        lineNumber: 329,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                lineNumber: 270,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_c1 = ModulesView;\n// Componente para a visualização de comparação\nconst ComparisonView = (param)=>{\n    let { modules: modules1, isAnnual, toggleModule, setActiveTab: setActiveTab1 } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"overflow-x-auto mb-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n            className: \"w-full bg-white dark:bg-gray-900 rounded-xl shadow-lg overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                        className: \"bg-gray-100 dark:bg-purple-900/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"py-4 px-6 text-left text-gray-700 dark:text-gray-300 font-medium\",\n                                children: \"Recurso\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                lineNumber: 348,\n                                columnNumber: 13\n                            }, undefined),\n                            modules1.map((module)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"py-4 px-6 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center justify-center w-8 h-8 rounded-lg mb-2 \".concat(module.colorClass),\n                                            children: module.icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-900 dark:text-white font-bold\",\n                                            children: module.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 354,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-500 dark:text-gray-400 text-sm\",\n                                            children: [\n                                                \"R$ \",\n                                                isAnnual ? (module.annualPrice / 12).toFixed(0) : module.monthlyPrice,\n                                                \"/m\\xeas\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 355,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, module.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 350,\n                                    columnNumber: 15\n                                }, undefined))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                        lineNumber: 347,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                    lineNumber: 346,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                    children: [\n                        [\n                            'Usuários incluídos',\n                            'Armazenamento',\n                            'Suporte',\n                            'API Access',\n                            'Integrações'\n                        ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"border-t border-gray-200 dark:border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"py-3 px-6 text-left text-gray-700 dark:text-gray-300\",\n                                        children: feature\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    modules1.map((module)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-3 px-6 text-center text-gray-600 dark:text-gray-400\",\n                                            children: [\n                                                feature === 'Usuários incluídos' && '5 usuários',\n                                                feature === 'Armazenamento' && (module.id === 'admin' ? '10GB' : '5GB'),\n                                                feature === 'Suporte' && (module.id === 'financial' || module.id === 'admin' ? 'Prioritário' : 'Padrão'),\n                                                feature === 'API Access' && (module.id === 'admin' || module.id === 'reports' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Info_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-5 w-5 text-green-500 mx-auto\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 25\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(X, {\n                                                    className: \"h-5 w-5 text-red-500 mx-auto\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 25\n                                                }, undefined)),\n                                                feature === 'Integrações' && (module.id === 'financial' || module.id === 'scheduler' || module.id === 'admin' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Info_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-5 w-5 text-green-500 mx-auto\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 25\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(X, {\n                                                    className: \"h-5 w-5 text-red-500 mx-auto\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 25\n                                                }, undefined))\n                                            ]\n                                        }, module.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 368,\n                                            columnNumber: 17\n                                        }, undefined))\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                lineNumber: 365,\n                                columnNumber: 13\n                            }, undefined)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            className: \"border-t border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"py-4 px-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, undefined),\n                                modules1.map((module)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"py-4 px-6 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setActiveTab1('modules');\n                                                setTimeout(()=>{\n                                                    scrollToElement('pricing', 80);\n                                                    toggleModule(module.id);\n                                                }, 100);\n                                            },\n                                            className: \"py-2 px-4 rounded-lg font-medium text-sm transition-colors \".concat(module.id === 'scheduler' ? 'bg-purple-600 hover:bg-purple-700 text-white' : module.id === 'people' ? 'bg-amber-600 hover:bg-amber-700 text-white' : module.id === 'financial' ? 'bg-emerald-600 hover:bg-emerald-700 text-white' : module.id === 'admin' ? 'bg-red-600 hover:bg-red-700 text-white' : 'bg-blue-600 hover:bg-blue-700 text-white'),\n                                            children: \"Selecionar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                            lineNumber: 396,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, module.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                                        lineNumber: 395,\n                                        columnNumber: 15\n                                    }, undefined))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                            lineNumber: 392,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n                    lineNumber: 362,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n            lineNumber: 345,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\Pricing.js\",\n        lineNumber: 344,\n        columnNumber: 5\n    }, undefined);\n};\n_c2 = ComparisonView;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Pricing);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Pricing\");\n$RefreshReg$(_c1, \"ModulesView\");\n$RefreshReg$(_c2, \"ComparisonView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/Pricing.js\n"));

/***/ })

});